package se.scmv.morocco.ad.listing

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import androidx.navigation.toRoute
import androidx.paging.PagingData
import androidx.paging.cachedIn
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.collections.immutable.PersistentList
import kotlinx.collections.immutable.persistentListOf
import kotlinx.collections.immutable.toPersistentList
import kotlinx.coroutines.ExperimentalCoroutinesApi
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.asSharedFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.combine
import kotlinx.coroutines.flow.first
import kotlinx.coroutines.flow.firstOrNull
import kotlinx.coroutines.flow.flatMapLatest
import kotlinx.coroutines.flow.onStart
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import se.scmv.morocco.ad.R
import se.scmv.morocco.ad.navigation.ListingRoute
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.designsystem.components.ChipData
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.coroutines.executeInParallel
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.CATEGORY_ID_ALL
import se.scmv.morocco.domain.models.CategoryTree
import se.scmv.morocco.domain.models.ListingAd
import se.scmv.morocco.domain.models.ORION_CATEGORY_KEY
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import se.scmv.morocco.domain.shared.BookmarkAdsEventManager
import se.scmv.morocco.orion.presentation.OrionFiltersSharedValuesManager
import se.scmv.morocco.orion.presentation.OrionFiltersValues
import se.scmv.morocco.ui.asUiText
import se.scmv.morocco.ui.renderFailure
import se.scmv.morocco.ui.renderSuccess
import javax.inject.Inject

@HiltViewModel
class AdsListingViewModel @Inject constructor(
    private val adRepository: AdRepository,
    private val accountRepository: AccountRepository,
    private val configRepository: ConfigRepository,
    private val adViewRepository: AdRepository,
    filtersSharedValuesManager: OrionFiltersSharedValuesManager,
    bookmarkAdsEventManager: BookmarkAdsEventManager,
    savedStateHandle: SavedStateHandle
) : ViewModel() {

    private val route = savedStateHandle.toRoute<ListingRoute>()

    private var selectedCategoryId: String = CATEGORY_ID_ALL
    private var selectedAdTypeKey: String? = AdTypeKey.ALL.name
    private var categoriesCash = emptyList<CategoryTree>()

    private val _viewState = MutableStateFlow(AdsListingViewState())
    val viewState = _viewState.asStateFlow()

    private val _oneTimeEvents = MutableSharedFlow<AdsListingOneTimeEvents>()
    val oneTimeEvents = _oneTimeEvents.asSharedFlow()

    private val _refreshTrigger = MutableSharedFlow<Unit>()

    private val _bookmarkUpdates = MutableStateFlow<Map<String, Boolean>>(mapOf())
    val bookmarkUpdates = _bookmarkUpdates.asStateFlow()

    @OptIn(ExperimentalCoroutinesApi::class)
    val ads: Flow<PagingData<ListingAd>> = combine(
        flow = filtersSharedValuesManager.filterValueSharedFlow,
        flow2 = _refreshTrigger.onStart { emit(Unit) },
        transform = { orionFiltersValues, _ -> orionFiltersValues }
    ).flatMapLatest { orionFiltersValues ->
        checkAndResetCategoriesIfNeeded(orionFiltersValues)
        val listingPagedAds = with(orionFiltersValues) {
            adRepository.getAds(filters = dynamicFilters, extendSearch = extendSearch)
        }
        _viewState.update { it.copy(extendedDelivery = listingPagedAds.extendedDelivery) }
        listingPagedAds.pagedAds
    }.cachedIn(viewModelScope)

    init {
        getPopularCategories()
        viewModelScope.launch {
            bookmarkAdsEventManager.events.collectLatest {
                updateBookmark(it.listId, it.isFavorite)
            }
        }
        viewModelScope.launch {
            val filters = extractFiltersFromRoute()
            filtersSharedValuesManager.initializeFiltersValues(filters.dynamicFilters)
            filtersSharedValuesManager.updateFilters(filters)
        }
        observeLanguageChanges()
    }

    fun onCategoryClicked(category: PopularCategory) {
        onCategoryChanged(categoryId = category.id, adTypeKey = category.adTypeKey)
    }

    fun onBackToParentCategories() {
        val categoryTree = CategoryTree.findTree(
            trees = categoriesCash,
            categoryId = selectedCategoryId,
            adTypeKey = selectedAdTypeKey?.let { AdTypeKey.safeValueOf(it) }
        ) ?: return
        if (categoryTree.children.isEmpty()) {
            val parentTree = CategoryTree.findParentTree(
                trees = categoriesCash,
                categoryId = selectedCategoryId,
                adTypeKey = selectedAdTypeKey?.let { AdTypeKey.safeValueOf(it) }
            ) ?: return
            onCategoryChanged(
                categoryId = parentTree.category.id,
                adTypeKey = parentTree.adTypes.firstOrNull()?.key?.name
            )
        } else {
            onCategoryChanged(categoryId = CATEGORY_ID_ALL, adTypeKey = AdTypeKey.ALL.name)
        }
    }

    private fun onCategoryChanged(categoryId: String, adTypeKey: String?) {
        viewModelScope.launch {
            selectedCategoryId = categoryId
            selectedAdTypeKey = adTypeKey
            refreshChildrenCategoriesAndFilters(
                categoryId = categoryId,
                adTypeKey = adTypeKey?.let { AdTypeKey.safeValueOf(it) }
            )
            _oneTimeEvents.emit(
                AdsListingOneTimeEvents.NotifyCategoryChanged(
                    categoryId = categoryId,
                    adTypeKey = adTypeKey
                )
            )
        }
    }

    fun updateAdFavoriteStatus(ad: ListingAd.Published) {
        viewModelScope.launch {
            val isLoggedIn = accountRepository.currentAccount.firstOrNull()?.isLogged() ?: false
            if (!isLoggedIn) {
                _oneTimeEvents.emit(AdsListingOneTimeEvents.NavigateToAuth)
                return@launch
            }
            val isFavorites = ad.isFavorite
            val result = if (isFavorites) {
                accountRepository.unBookmarkAd(ad.listId)
            } else {
                accountRepository.bookmarkAd(ad.listId)
            }
            when (result) {
                is Resource.Success -> {
                    val message = if (isFavorites) {
                        R.string.bookmarked_ads_screen_delete_ad_success
                    } else R.string.bookmarked_ads_screen_bookmark_ad_success
                    renderSuccess(UiText.FromRes(message))
                    updateBookmark(ad.listId, isFavorites.not())
                }

                is Resource.Failure -> renderFailure(result.error)
            }
        }
    }

    fun sendFirstMessage(adId: String, text: String) {
        if (adId.isBlank()) {
            renderFailure(UiText.FromRes(R.string.common_unexpected_error_verify_and_try_later))
            return
        }

        viewModelScope.launch {
            _oneTimeEvents.emit(AdsListingOneTimeEvents.ShowHideProgress(true))

            val result = adViewRepository.sendFirstMessageToSeller(adId, text)

            _oneTimeEvents.emit(AdsListingOneTimeEvents.ShowHideProgress(false))

            when (result) {
                is Resource.Success -> {
                    renderSuccess(UiText.FromRes(R.string.message_sent_successfully))
                }

                is Resource.Failure -> {
                    renderFailure(result.error.asUiText())
                }
            }
        }
    }

    fun handleMessagingClick(adId: String?, ad: ListingAd) {
        viewModelScope.launch {
            val account = accountRepository.currentAccount.first()
            if (account !is Account.Connected) {
                _oneTimeEvents.emit(AdsListingOneTimeEvents.NavigateToAuth)
                return@launch
            }

            _oneTimeEvents.emit(AdsListingOneTimeEvents.ShowHideProgress(true))

            val result = adId?.let { safeId ->
                adViewRepository.getConversationIdWithAd(safeId)
            }

            _oneTimeEvents.emit(AdsListingOneTimeEvents.ShowHideProgress(false))

            when (result) {
                is Resource.Success -> {
                    val data = result.data
                    if (data.found) {
                        _oneTimeEvents.emit(
                            AdsListingOneTimeEvents.ShowConversation(data.conversationId!!)
                        )
                    } else {
                        _oneTimeEvents.emit(
                            AdsListingOneTimeEvents.ShowFirstConversationBtmSheet(adId ?: "", ad)
                        )
                    }
                }

                is Resource.Failure -> {
                    _oneTimeEvents.emit(
                        AdsListingOneTimeEvents.ShowFirstConversationBtmSheet(adId ?: "", ad)
                    )
                }

                null -> {
                    _oneTimeEvents.emit(
                        AdsListingOneTimeEvents.ShowFirstConversationBtmSheet(adId ?: "", ad)
                    )
                }
            }
        }
    }

    private suspend fun extractFiltersFromRoute(): OrionFiltersValues {
        val params = route.params.map { "${it.key}=${it.value}" }.joinToString("&")
        if (params.isBlank()) {
            return OrionFiltersValues(
                dynamicFilters = emptyList(),
                extendSearch = true
            )
        }
        val dynamicFilters = configRepository.buildFiltersValuesFor(params)
        val filters = OrionFiltersValues(
            dynamicFilters = dynamicFilters,
            extendSearch = true
        )
        return filters
    }

    private fun checkAndResetCategoriesIfNeeded(orionFiltersValues: OrionFiltersValues) {
        val category = orionFiltersValues.dynamicFilters
            .filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
            .firstOrNull { it.id == ORION_CATEGORY_KEY }
        if (category != null) {
            viewModelScope.launch {
                selectedCategoryId = category.categoryId
                selectedAdTypeKey = category.adTypeKey?.name
                refreshChildrenCategoriesAndFilters(
                    categoryId = category.categoryId,
                    adTypeKey = category.adTypeKey
                )
            }
        }
    }

    private fun updateBookmark(listId: String, isFavorite: Boolean) {
        _bookmarkUpdates.update { map -> map + (listId to isFavorite) }
    }

    private fun getPopularCategories() {
        viewModelScope.launch {
            when (
                val result = executeInParallel(
                    firstCall = { configRepository.getListingCategories() },
                    secondCall = { configRepository.getFiltersCategories() }
                )
            ) {
                is Resource.Success -> {
                    val popularCategories = result.data.first.mapNotNull {
                        CategoryTree.findTree(
                            trees = result.data.second,
                            categoryId = it,
                            adTypeKey = null
                        )
                    }.toPopularCategories().toPersistentList()
                    categoriesCash = result.data.second
                    _viewState.update { state -> state.copy(popularCategories = popularCategories) }
                }

                is Resource.Failure -> Unit
            }
        }
    }

    private fun observeLanguageChanges() {
        viewModelScope.launch {
            LocaleManager.languageChangeEvent.collectLatest {
                _refreshTrigger.emit(Unit)
                getPopularCategories()
            }
        }
    }

    private suspend fun refreshChildrenCategoriesAndFilters(
        categoryId: String,
        adTypeKey: AdTypeKey?
    ) {
        if (categoryId == CATEGORY_ID_ALL && adTypeKey == AdTypeKey.ALL) {
            _viewState.update { state ->
                state.copy(
                    childrenCategories = persistentListOf(),
                    canBackToParentCategories = false,
                    filters = persistentListOf()
                )
            }
        } else {
            val filters = getCategoryFilters(categoryId, adTypeKey)
            val tree = CategoryTree.findTree(
                trees = categoriesCash,
                categoryId = categoryId,
                adTypeKey = adTypeKey
            )
            if (tree != null) {
                val children = tree.children.toPopularCategories()
                if (children.isNotEmpty()) {
                    _viewState.update { state ->
                        state.copy(
                            childrenCategories = children.toPersistentList(),
                            canBackToParentCategories = true,
                            filters = filters
                        )
                    }
                } else {
                    val parentTree = CategoryTree.findParentTree(
                        trees = categoriesCash,
                        categoryId = categoryId,
                        adTypeKey = adTypeKey
                    )
                    val parentTreeChildren = parentTree?.children?.toPopularCategories()?.map {
                        it.copy(enabled = it.id != categoryId)
                    }
                    if (!parentTreeChildren.isNullOrEmpty()) {
                        _viewState.update { state ->
                            state.copy(
                                childrenCategories = parentTreeChildren.toPersistentList(),
                                canBackToParentCategories = true,
                                filters = filters
                            )
                        }
                    }
                }
            }
        }
    }

    private suspend fun getCategoryFilters(
        categoryId: String,
        adTypeKey: AdTypeKey?
    ): PersistentList<ChipData> {
        return when (
            val result = configRepository.getFilters(
                categoryId = categoryId,
                adTypeKey = adTypeKey ?: AdTypeKey.SELL
            )
        ) {
            is Resource.Success -> {
                result.data.map {
                    with(it.baseData) {
                        ChipData(id = id, name = title, selected = false)
                    }
                }.toPersistentList()
            }

            is Resource.Failure -> persistentListOf()
        }
    }

    private fun List<CategoryTree>.toPopularCategories() = map { it.toPopularCategory() }

    private fun CategoryTree.toPopularCategory() = PopularCategory(
        id = category.id,
        name = category.name,
        icon = category.icon,
        adTypeKey = adTypes.firstOrNull()?.key?.name
    )
}