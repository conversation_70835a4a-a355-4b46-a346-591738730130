package se.scmv.morocco.ad.navigation

/**
 * Utility class for handling additional parameters from marketing deep links.
 * This class provides methods to process unknown parameters that may be sent
 * by the marketing team in deep links.
 */
object DeepLinkParamsHandler {

    /**
     * Known parameter types that we can handle specifically
     */
    enum class ParameterType {
        UTM,           // UTM tracking parameters (utm_source, utm_medium, etc.)
        REFERRER,      // Referrer tracking (ref, referrer)
        PROMOTION,     // Promotional codes (promo, promotion_code, coupon)
        CAMPAIGN,      // Campaign tracking (campaign_id, campaign_name)
        ANALYTICS,     // Analytics parameters (fbclid, gclid, etc.)
        UNKNOWN        // Any other parameters
    }

    /**
     * Process all additional parameters from a ListingRoute
     */
    fun processAdditionalParams(
        additionalParams: Map<String, String>,
        onUTMParam: ((key: String, value: String) -> Unit)? = null,
        onReferrerParam: ((value: String) -> Unit)? = null,
        onPromotionParam: ((value: String) -> Unit)? = null,
        onCampaignParam: ((key: String, value: String) -> Unit)? = null,
        onAnalyticsParam: ((key: String, value: String) -> Unit)? = null,
        onUnknownParam: ((key: String, value: String) -> Unit)? = null
    ) {
        additionalParams.forEach { (key, value) ->
            when (getParameterType(key)) {
                ParameterType.UTM -> onUTMParam?.invoke(key, value)
                ParameterType.REFERRER -> onReferrerParam?.invoke(value)
                ParameterType.PROMOTION -> onPromotionParam?.invoke(value)
                ParameterType.CAMPAIGN -> onCampaignParam?.invoke(key, value)
                ParameterType.ANALYTICS -> onAnalyticsParam?.invoke(key, value)
                ParameterType.UNKNOWN -> onUnknownParam?.invoke(key, value)
            }
        }
    }

    /**
     * Determine the type of parameter based on its key
     */
    private fun getParameterType(key: String): ParameterType {
        return when (key.lowercase()) {
            "utm_source", "utm_medium", "utm_campaign", "utm_content", "utm_term", "utm_id" -> {
                ParameterType.UTM
            }
            "ref", "referrer", "source" -> {
                ParameterType.REFERRER
            }
            "promo", "promotion_code", "coupon", "discount_code" -> {
                ParameterType.PROMOTION
            }
            "campaign_id", "campaign_name", "campaign_type" -> {
                ParameterType.CAMPAIGN
            }
            "fbclid", "gclid", "msclkid", "ttclid" -> {
                ParameterType.ANALYTICS
            }
            else -> ParameterType.UNKNOWN
        }
    }

    /**
     * Extract UTM parameters specifically
     */
    fun extractUTMParams(additionalParams: Map<String, String>): Map<String, String> {
        return additionalParams.filterKeys { key ->
            key.lowercase().startsWith("utm_")
        }
    }

    /**
     * Check if the parameters contain any tracking information
     */
    fun hasTrackingParams(additionalParams: Map<String, String>): Boolean {
        return additionalParams.keys.any { key ->
            getParameterType(key) in listOf(
                ParameterType.UTM,
                ParameterType.REFERRER,
                ParameterType.CAMPAIGN,
                ParameterType.ANALYTICS
            )
        }
    }

    /**
     * Create a summary of all parameters for logging/debugging
     */
    fun createParameterSummary(additionalParams: Map<String, String>): String {
        if (additionalParams.isEmpty()) return "No additional parameters"
        
        val summary = StringBuilder("Deep link parameters:\n")
        additionalParams.forEach { (key, value) ->
            val type = getParameterType(key)
            summary.append("  $key = $value (${type.name})\n")
        }
        return summary.toString()
    }
}

/**
 * Extension function for ListingRoute to easily process additional parameters
 */
fun ListingRoute.processAdditionalParams(
    onUTMParam: ((key: String, value: String) -> Unit)? = null,
    onReferrerParam: ((value: String) -> Unit)? = null,
    onPromotionParam: ((value: String) -> Unit)? = null,
    onCampaignParam: ((key: String, value: String) -> Unit)? = null,
    onAnalyticsParam: ((key: String, value: String) -> Unit)? = null,
    onUnknownParam: ((key: String, value: String) -> Unit)? = null
) {
    DeepLinkParamsHandler.processAdditionalParams(
        additionalParams = this.additionalParams,
        onUTMParam = onUTMParam,
        onReferrerParam = onReferrerParam,
        onPromotionParam = onPromotionParam,
        onCampaignParam = onCampaignParam,
        onAnalyticsParam = onAnalyticsParam,
        onUnknownParam = onUnknownParam
    )
}

/**
 * Extension function to get UTM parameters from ListingRoute
 */
fun ListingRoute.getUTMParams(): Map<String, String> {
    return DeepLinkParamsHandler.extractUTMParams(this.additionalParams)
}

/**
 * Extension function to check if ListingRoute has tracking parameters
 */
fun ListingRoute.hasTrackingParams(): Boolean {
    return DeepLinkParamsHandler.hasTrackingParams(this.additionalParams)
}
