package se.scmv.morocco.ad.navigation

import kotlinx.serialization.KSerializer
import kotlinx.serialization.SerializationException
import kotlinx.serialization.descriptors.SerialDescriptor
import kotlinx.serialization.descriptors.buildClassSerialDescriptor
import kotlinx.serialization.descriptors.element
import kotlinx.serialization.encoding.Decoder
import kotlinx.serialization.encoding.Encoder
import kotlinx.serialization.encoding.decodeStructure
import kotlinx.serialization.encoding.encodeStructure
import kotlinx.serialization.json.JsonDecoder
import kotlinx.serialization.json.JsonEncoder
import kotlinx.serialization.json.JsonObject
import kotlinx.serialization.json.JsonPrimitive
import kotlinx.serialization.json.jsonObject
import kotlinx.serialization.json.jsonPrimitive

/**
 * Custom serializer for ListingRoute that can handle dynamic parameters
 * from marketing deep links while preserving known parameters like category and adType.
 */
object DynamicListingRouteSerializer : KSerializer<ListingRoute> {
    override val descriptor: SerialDescriptor = buildClassSerialDescriptor("ListingRoute") {
        element<String?>("category")
        element<String?>("adType")
        element<Map<String, String>>("additionalParams")
    }

    override fun serialize(encoder: Encoder, value: ListingRoute) {
        encoder.encodeStructure(descriptor) {
            encodeStringElement(descriptor, 0, value.category)
            encodeStringElement(descriptor, 1, value.adType)
            
            // Encode additional params as a map
            if (encoder is JsonEncoder) {
                val jsonObject = buildMap<String, JsonPrimitive> {
                    value.additionalParams.forEach { (key, paramValue) ->
                        put(key, JsonPrimitive(paramValue))
                    }
                }
                encodeSerializableElement(descriptor, 2, kotlinx.serialization.builtins.MapSerializer(
                    kotlinx.serialization.builtins.serializer<String>(),
                    kotlinx.serialization.builtins.serializer<String>()
                ), value.additionalParams)
            }
        }
    }

    override fun deserialize(decoder: Decoder): ListingRoute {
        return decoder.decodeStructure(descriptor) {
            var category: String? = null
            var adType: String? = null
            var additionalParams: Map<String, String> = emptyMap()

            while (true) {
                when (val index = decodeElementIndex(descriptor)) {
                    0 -> category = decodeStringElement(descriptor, 0)
                    1 -> adType = decodeStringElement(descriptor, 1)
                    2 -> additionalParams = decodeSerializableElement(
                        descriptor, 2, kotlinx.serialization.builtins.MapSerializer(
                            kotlinx.serialization.builtins.serializer<String>(),
                            kotlinx.serialization.builtins.serializer<String>()
                        )
                    )
                    kotlinx.serialization.encoding.CompositeDecoder.DECODE_DONE -> break
                    else -> throw SerializationException("Unknown index $index")
                }
            }

            ListingRoute(category, adType, additionalParams)
        }
    }
}

/**
 * Extension function to extract all parameters from a URL query string
 * and separate known parameters from unknown ones.
 */
fun String.parseDeepLinkParams(): ListingRoute {
    val params = mutableMapOf<String, String>()
    
    // Parse query parameters from URL
    val queryStart = this.indexOf('?')
    if (queryStart != -1) {
        val queryString = this.substring(queryStart + 1)
        queryString.split('&').forEach { param ->
            val keyValue = param.split('=', limit = 2)
            if (keyValue.size == 2) {
                val key = keyValue[0].trim()
                val value = keyValue[1].trim()
                if (key.isNotEmpty() && value.isNotEmpty()) {
                    params[key] = value
                }
            }
        }
    }
    
    // Extract known parameters
    val category = params.remove("category")
    val adType = params.remove("adType")
    
    // Remaining parameters go to additionalParams
    return ListingRoute(
        category = category,
        adType = adType,
        additionalParams = params
    )
}
