package se.scmv.morocco.ad.navigation

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavGraphBuilder
import androidx.navigation.NavHostController
import androidx.navigation.NavOptions
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.navArgument
import androidx.navigation.navDeepLink
import androidx.navigation.navOptions
import androidx.navigation.toRoute
import kotlinx.serialization.SerialName
import kotlinx.serialization.Serializable
import se.scmv.morocco.ad.R
import se.scmv.morocco.ad.ad_view.AdViewScreen
import se.scmv.morocco.ad.ad_view.PDFViewerScreen
import se.scmv.morocco.ad.insert.AdInsertResultScreen
import se.scmv.morocco.ad.insert.AdInsertRoute
import se.scmv.morocco.ad.vas.master.IntentParams
import se.scmv.morocco.ad.vas.master.VasMasterRoute
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AdDetails
import se.scmv.morocco.domain.models.VasPacksApplication
import se.scmv.morocco.ui.AppDeepLinks
import se.scmv.morocco.ui.AuthProtectedContent
import se.scmv.morocco.ui.WebDeepLinks
import se.scmv.morocco.ui.composableWithAnimation

const val ARG_AD_ID = "arg_ad_id"
const val ARG_GO_TO_IMAGE_STEP = "arg_go_to_image_step"

enum class AdScreen(val route: String) {
    INSERT("ad_insert?$ARG_AD_ID={$ARG_AD_ID}&$ARG_GO_TO_IMAGE_STEP={$ARG_GO_TO_IMAGE_STEP}"),
    INSERT_RESULT("ad_insert_result"),
}

@Composable
fun AdNavHost(
    modifier: Modifier = Modifier,
    account: Account.Connected,
    adId: String? = null,
    goToImageStep: Boolean,
    navController: NavHostController,
    navigateBack: () -> Unit,
    navigateToVas: (IntentParams) -> Unit,
    navigateToAccountAds: () -> Unit
) {
    NavHost(
        modifier = modifier,
        navController = navController,
        startDestination = if (adId != null) {
            AdScreen.INSERT.route
                .replace("{$ARG_AD_ID}", adId)
                .replace("{$ARG_GO_TO_IMAGE_STEP}", goToImageStep.toString())
        } else {
            AdScreen.INSERT.route.replace(
                oldValue = "?$ARG_AD_ID={$ARG_AD_ID}&$ARG_GO_TO_IMAGE_STEP={$ARG_GO_TO_IMAGE_STEP}",
                newValue = ""
            )
        }
    ) {
        composableWithAnimation(
            route = AdScreen.INSERT.route,
            arguments = listOf(
                navArgument(ARG_AD_ID) {
                    type = NavType.StringType
                    nullable = true
                },
                navArgument(ARG_GO_TO_IMAGE_STEP) {
                    type = NavType.BoolType
                    nullable = false
                    defaultValue = false
                }
            )
        ) {
            AdInsertRoute(
                account = account,
                navigateBack = navigateBack,
                navigateToInsertResultScreen = {
                    navController.navigate(
                        route = AdScreen.INSERT_RESULT.route,
                        navOptions = navOptions {
                            popUpTo(route = AdScreen.INSERT.route) {
                                inclusive = true
                            }
                        }
                    )
                },
                navigateToVas = {

                }
            )
        }
        composableWithAnimation(
            route = AdScreen.INSERT_RESULT.route,
        ) {
            AdInsertResultScreen(
                accountName = account.connectedContact().name,
                navigateBack = navigateBack,
                navigateToAccountAds = navigateToAccountAds
            )
        }
    }
}

// OFFICIAL NAVIGATION

@Serializable
data class ListingRoute(val params: String)

@Serializable
data class AdViewRoute(val adId: String)

@Serializable
data class PdfViewerRoute(val encodedUrl: String)

@Serializable
data class AdInsertRoute(val adId: String? = null, val goToImageStep: Boolean = false)

@Serializable
data class AdInsertResultRoute(val from: VasRoute.From)

@Serializable
data class VasRoute(
    val from: From,
    val adId: String,
    val application: VasPacksApplication,
    val chosenVasPackKey: String? = null,
    @SerialName("pack_id") val chosenVasPackageId: String? = null,
    val chosenExecutionSlotsDay: String? = null,
    val chosenExecutionSlotsTimeId: String? = null,
) {
    @Serializable
    enum class From(val trackingName: String) {
        AD_INSERT("ad_insert"),
        AD_VIEW("ad_view"),
        ACCOUNT("my_account"),
        NOTIFICATION("notification"),
    }
}

fun NavGraphBuilder.adNavGraph(
    account: Account,
    navController: NavHostController,
    navigateToAuthentication: () -> Unit,
    navigateToAccountAds: (NavOptions) -> Unit,
    navigateToWebViewScreen: (String?, String, NavOptions?) -> Unit,
    navigateToShopPage: (String) -> Unit,
    navigateToChat: (conversationId: String) -> Unit,
) {
    composableWithAnimation<AdViewRoute>(
        deepLinks = listOf(
            navDeepLink<AdViewRoute>(basePath = AppDeepLinks.AD_VIEW),
            navDeepLink<AdViewRoute>(basePath = WebDeepLinks.AD_VIEW_HTTP),
            navDeepLink<AdViewRoute>(basePath = WebDeepLinks.AD_VIEW_HTTPS),
        )
    ) {
        AdViewScreen(
            navigateBack = { navController.navigateUp() },
            navigateToSimilarAd = { listId, _, _, _, _, _, _, _, _, _, _, _, _, _, _ ->
                navController.navigate(AdViewRoute(listId))
            },
            onOpenPdf = { navController.navigate(PdfViewerRoute(encodedUrl = it)) },
            onShopPage = navigateToShopPage,
            onBuyEcommerceProduct = { details: AdDetails.Details, s: String -> },
            navigateToAuthentication = navigateToAuthentication,
            showMessageAfterAuth = false,
            onBoostClick = { adId, adCategoryKey, adType, application ->
                navController.navigate(
                    VasRoute(
                        from = VasRoute.From.AD_VIEW,
                        adId = adId,
                        application = application
                    )
                )
            },
            navigateToChat = navigateToChat,
            navigateToWebViewScreen = { title, url ->
                navigateToWebViewScreen(title, url, null)
            }
        )
    }
    composableWithAnimation<PdfViewerRoute> {
        val route = it.toRoute<PdfViewerRoute>()
        PDFViewerScreen(
            pdfUrl = route.encodedUrl,
            navigateBack = { navController.navigateUp() }
        )
    }
    composableWithAnimation<AdInsertRoute>(
        deepLinks = listOf(
            navDeepLink<AdInsertRoute>(basePath = AppDeepLinks.AD_INSERT),
            navDeepLink<AdInsertRoute>(basePath = WebDeepLinks.AD_INSERT_HTTP),
            navDeepLink<AdInsertRoute>(basePath = WebDeepLinks.AD_INSERT_HTTPS),
        )
    ) {
        val route = it.toRoute<AdInsertRoute>()
        AuthProtectedContent(
            account = account,
            onLoginClicked = navigateToAuthentication
        ) { connectedAccount ->
            AdInsertRoute(
                account = connectedAccount,
                navigateBack = { navController.navigateUp() },
                navigateToInsertResultScreen = { from ->
                    navController.navigate(AdInsertResultRoute(from = from)) {
                        popUpTo(route) {
                            inclusive = true
                        }
                    }
                },
                navigateToVas = { vas ->
                    navController.navigate(vas) {
                        popUpTo(route) {
                            inclusive = true
                        }
                    }
                }
            )
        }
    }
    composableWithAnimation<AdInsertResultRoute> {
        val route = it.toRoute<AdInsertResultRoute>()
        AuthProtectedContent(
            account = account,
            onLoginClicked = navigateToAuthentication
        ) { connectedAccount ->
            AdInsertResultScreen(
                accountName = connectedAccount.connectedContact().name,
                navigateBack = { navController.navigateUp() },
                navigateToAccountAds = {
                    if (route.from == VasRoute.From.ACCOUNT) {
                        navController.navigateUp()
                    } else if (route.from == VasRoute.From.AD_INSERT) {
                        navigateToAccountAds(
                            navOptions {
                                popUpTo(route) {
                                    inclusive = true
                                }
                            }
                        )
                    }
                },
            )
        }
    }
    composableWithAnimation<VasRoute>(
        deepLinks = listOf(
            navDeepLink<VasRoute>(basePath = AppDeepLinks.AD_BUMP),
            navDeepLink<VasRoute>(basePath = WebDeepLinks.AD_BUMP_HTTP),
            navDeepLink<VasRoute>(basePath = WebDeepLinks.AD_BUMP_HTTPS),
        )
    ) {
        AuthProtectedContent(
            account = account,
            onLoginClicked = navigateToAuthentication
        ) { connectedAccount ->
            val route = it.toRoute<VasRoute>()
            val title = stringResource(R.string.webview_title_credit_card_payment)
            VasMasterRoute(
                account = connectedAccount,
                closeVas = { navController.navigateUp() },
                openUrl = { url, isCreditCardPayment ->
                    if (isCreditCardPayment) {
                        navigateToWebViewScreen(
                            title,
                            url,
                            navOptions {
                                popUpTo(route) {
                                    inclusive = true
                                }
                            }
                        )
                    } else {
                        navigateToWebViewScreen(null, url, null)
                    }
                },
                navigateToAccountAds = {
                    navigateToAccountAds(
                        navOptions {
                            popUpTo(route) {
                                inclusive = true
                            }
                        }
                    )
                },
                navigateToAdInsertImageStep = {
                    navController.navigate(
                        AdInsertRoute(adId = route.adId, goToImageStep = true)
                    ) {
                        if (route.from == VasRoute.From.AD_INSERT) {
                            popUpTo(AdInsertRoute(adId = route.adId, goToImageStep = true)) {
                                inclusive = true
                            }
                        } else if (route.from == VasRoute.From.ACCOUNT) {
                            popUpTo(route) {
                                inclusive = true
                            }
                        }
                    }
                },
            )
        }
    }
}