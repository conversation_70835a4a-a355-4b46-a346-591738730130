package se.scmv.morocco.orion.presentation

import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.asSharedFlow
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import javax.inject.Inject

data class OrionFiltersValues(
    val dynamicFilters: List<OrionBaseComponentValue> = emptyList(),
    val extendSearch: Boolean = true
)

class OrionFiltersSharedValuesManager @Inject constructor() {
    private var latestFiltersValues = OrionFiltersValues()

    private val _filterValueSharedFlow = MutableSharedFlow<OrionFiltersValues>()
    val filterValueSharedFlow = _filterValueSharedFlow.asSharedFlow()

    private val _initialFiltersValues = MutableSharedFlow<List<OrionBaseComponentValue>>()
    val initialFiltersValues = _initialFiltersValues.asSharedFlow()

    suspend fun updateFilters(filterValue: OrionFiltersValues) {
        latestFiltersValues = filterValue
        _filterValueSharedFlow.emit(filterValue)
    }

    suspend fun initializeFiltersValues(filterValues: List<OrionBaseComponentValue>) {
        _initialFiltersValues.emit(filterValues)
    }
}
