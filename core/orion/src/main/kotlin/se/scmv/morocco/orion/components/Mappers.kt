package se.scmv.morocco.orion.components

import androidx.compose.ui.util.fastAny
import androidx.compose.ui.util.fastFilter
import androidx.compose.ui.util.fastFirstOrNull
import androidx.compose.ui.util.fastMap
import kotlinx.coroutines.flow.StateFlow
import se.scmv.morocco.designsystem.utils.UiText
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.CategoryTree
import se.scmv.morocco.domain.models.VasPack
import se.scmv.morocco.domain.models.VasPackage
import se.scmv.morocco.domain.models.orion.OrionAlert
import se.scmv.morocco.domain.models.orion.OrionBaseComponent
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionGroupHeader
import se.scmv.morocco.domain.models.orion.OrionImageUploader
import se.scmv.morocco.domain.models.orion.OrionKeyBooleanValue
import se.scmv.morocco.domain.models.orion.OrionKeyRangeValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringListValue
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionLap
import se.scmv.morocco.domain.models.orion.OrionMediaUploaderValue
import se.scmv.morocco.domain.models.orion.OrionMinMax
import se.scmv.morocco.domain.models.orion.OrionMultipleSelectSmartDropDown
import se.scmv.morocco.domain.models.orion.OrionNativeDropdown
import se.scmv.morocco.domain.models.orion.OrionNumberCounter
import se.scmv.morocco.domain.models.orion.OrionSelectExtended
import se.scmv.morocco.domain.models.orion.OrionSelectedKeysValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdown
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.domain.models.orion.OrionSlider
import se.scmv.morocco.domain.models.orion.OrionSmartDropDown
import se.scmv.morocco.domain.models.orion.OrionTextField
import se.scmv.morocco.domain.models.orion.OrionTimePicker
import se.scmv.morocco.domain.models.orion.OrionToggle
import se.scmv.morocco.domain.models.orion.OrionVas
import se.scmv.morocco.domain.models.orion.OrionVideoUploader
import se.scmv.morocco.orion.components.media.OrionUiComponentImageUploader
import se.scmv.morocco.orion.components.media.OrionUiComponentMedia
import se.scmv.morocco.orion.components.media.OrionUiComponentVideoUploader
import se.scmv.morocco.ui.SnackBarType

interface OptionalItemsListener {
    fun onPrincipalImageChanged(image: OrionUiComponentMedia?) {}
    fun onVasPackageSelected(vasPack: VasPack, vasPackage: VasPackage) {}
    fun onExeSlotDaySelected(day: String) {}
    fun onExeSlotTimeSelected(timeId: String) {}
    fun onAddImageClicked() {}
    fun showSnackBar(uiText: UiText, type: SnackBarType) {}
    fun executeVasAction(action: VasAction) {}
    fun onValueChanged(component: OrionBaseComponent, values: List<OrionBaseComponentValue>) {}
}

fun OrionBaseComponent.toOrionUiComponent(
    categories: List<CategoryTree>,
    categoryId: String,
    adTypeKey: AdTypeKey,
    hasNextTextFieldItem: Boolean,
    initialValues: List<OrionBaseComponentValue>,
    listener: OptionalItemsListener,
    lapViewState: StateFlow<AdInsertLapViewState>? = null
): OrionUiComponent? {
    val stringValues = initialValues.filterIsInstance<OrionKeyStringValue>()
    val stringListValues = initialValues.filterIsInstance<OrionKeyStringListValue>()
    val stringSelectedKeysValue = initialValues.filterIsInstance<OrionSelectedKeysValue>()
    val booleanValues = initialValues.filterIsInstance<OrionKeyBooleanValue>()
    val rangeValues = initialValues.filterIsInstance<OrionKeyRangeValue>()
    return when (this) {
        is OrionSingleSelectCategoryDropdown -> {
            val (initialValue, enabled) = initialValues.filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
                .fastFirstOrNull { it.id == baseData.id }
                ?.let {
                    OrionSingleSelectCategoryDropdownValue(
                        id = baseData.id,
                        categoryId = it.categoryId,
                        adTypeKey = it.adTypeKey
                    ) to allowParentSelection
                } ?: run {
                OrionSingleSelectCategoryDropdownValue(
                    id = baseData.id,
                    categoryId = categoryId,
                    adTypeKey = adTypeKey
                ) to true
            }

            OrionUiComponentSingleSelectCategoryDropdown(
                uiConfig = this.copy(enabled = enabled),
                initialValue = initialValue,
                categories = categories,
                listener = listener
            )
        }

        is OrionGroupHeader -> OrionUiComponentGroupHeader(uiConfig = this)
        is OrionTextField -> OrionUiComponentTextField(
            uiConfig = this,
            hasNextTextFieldItem = hasNextTextFieldItem,
            listener = listener,
            initialValue = initialValues.fastFirstOrNull { it.id == baseData.id }?.let {
                OrionKeyStringValue(id = it.id, value = it.stringValue())
            }
        )

        is OrionToggle -> OrionUiComponentToggle(
            uiConfig = this,
            listener = listener,
            initialValue = booleanValues.fastFirstOrNull { it.id == baseData.id },
        )

        is OrionNumberCounter -> OrionUiComponentNumberCounter(
            uiConfig = this,
            listener = listener,
            initialValue = stringValues.fastFirstOrNull { it.id == baseData.id }
        )

        is OrionSelectExtended -> {
            if (allowMultiSelect) {
                OrionUiComponentMultipleSelectExtended(
                    uiConfig = this,
                    listener = listener,
                    initialValue = getMultiSelectExtendedInitialValue(
                        component = this,
                        stringSelectedKeysValue = stringSelectedKeysValue,
                        stringListValues = stringListValues,
                        booleanValues = booleanValues
                    )
                )
            } else OrionUiComponentSingleSelectExtended(
                uiConfig = this,
                listener = listener,
                initialValue = stringValues.fastFirstOrNull { it.id == baseData.id }
            )
        }

        is OrionTimePicker -> OrionUiComponentTimePicker(
            uiConfig = this,
            listener = listener,
            initialValue = stringValues.fastFirstOrNull { it.id == baseData.id }
        )

        is OrionNativeDropdown -> OrionUiComponentNativeDropdown(
            uiConfig = this,
            listener = listener,
            initialValue = stringValues.fastFirstOrNull { it.id == baseData.id }
        )

        is OrionAlert -> OrionUiComponentAlert(uiConfig = this)
        is OrionImageUploader -> OrionUiComponentImageUploader(
            uiConfig = this,
            initialValue = initialValues.filterIsInstance<OrionMediaUploaderValue>()
                .fastFirstOrNull { it.id == baseData.id },
            onPrincipalImageChanged = listener::onPrincipalImageChanged
        )

        is OrionVideoUploader -> OrionUiComponentVideoUploader(
            uiConfig = this,
            initialValue = initialValues.filterIsInstance<OrionMediaUploaderValue>()
                .fastFirstOrNull { it.id == baseData.id },
        )

        is OrionLap -> lapViewState?.let { OrionUiComponentLap(uiConfig = this, viewState = it) }
        is OrionVas -> lapViewState?.let {
            OrionUiComponentVas(
                uiConfig = this,
                lapViewState = it,
                onVasPackageSelected = listener::onVasPackageSelected,
                onExeSlotDaySelected = listener::onExeSlotDaySelected,
                onExeSlotTimeSelected = listener::onExeSlotTimeSelected,
                onAddImageClicked = listener::onAddImageClicked,
                showSnackBar = listener::showSnackBar
            )
        }

        is OrionSmartDropDown -> OrionUiComponentSmartDropdown(
            uiConfig = this,
            listener = listener,
            initialParentValue = stringValues.fastFirstOrNull { it.id == baseData.id },
            initialChildValue = stringValues.fastFirstOrNull { it.id == childBaseData.id },
        )

        is OrionMultipleSelectSmartDropDown -> OrionUiComponentMultipleSelectSmartDropdown(
            uiConfig = this,
            listener = listener,
            initialParentValue = getMultiSelectDropdownInitialValue(
                id = baseData.id,
                stringValues = stringValues,
                stringListValues = stringListValues
            ),
            initialChildValue = getMultiSelectDropdownInitialValue(
                id = childBaseData.id,
                stringValues = stringValues,
                stringListValues = stringListValues
            ),
        )

        is OrionMinMax -> OrionUiComponentMinMax(
            uiConfig = this,
            listener = listener,
            initialValue = rangeValues.fastFirstOrNull { it.id == baseData.id }
        )

        is OrionSlider -> OrionUiComponentSlider(
            uiConfig = this,
            listener = listener,
            initialValue = rangeValues.fastFirstOrNull { it.id == baseData.id }
        )
    }
}

private fun getMultiSelectDropdownInitialValue(
    id: String,
    stringValues: List<OrionKeyStringValue>,
    stringListValues: List<OrionKeyStringListValue>
): OrionKeyStringListValue? {
    // Case of multiple select, example: casablanca,agadir or dacia logan, dacia sandero
    val value = stringListValues.fastFirstOrNull { it.id == id }
    if (value != null) return value

    // Case of single select, example: casablanca ain diab or dacia logan
    val stringValue = stringValues.fastFirstOrNull { it.id == id }
    if (stringValue != null) return OrionKeyStringListValue(
        id = stringValue.id,
        items = listOf(stringValue.value)
    )

    return null
}

private fun getMultiSelectExtendedInitialValue(
    component: OrionSelectExtended,
    stringSelectedKeysValue: List<OrionSelectedKeysValue>,
    stringListValues: List<OrionKeyStringListValue>,
    booleanValues: List<OrionKeyBooleanValue>,
): OrionSelectedKeysValue? {
    val value = stringSelectedKeysValue.fastFirstOrNull { it.id == component.baseData.id }
    if (value != null) return value

    val stringValue = stringListValues.fastFirstOrNull { it.id == component.baseData.id }
    if (stringValue != null) return OrionSelectedKeysValue(
        id = stringValue.id,
        keys = stringValue.items
    )

    val booleanValues = component.items.fastFilter { item ->
        booleanValues.fastAny { it.id == item.id && it.value }
    }.fastMap { it.id }
    if (booleanValues.isNotEmpty()) return OrionSelectedKeysValue(
        id = component.baseData.id,
        keys = booleanValues
    )

    return null
}

