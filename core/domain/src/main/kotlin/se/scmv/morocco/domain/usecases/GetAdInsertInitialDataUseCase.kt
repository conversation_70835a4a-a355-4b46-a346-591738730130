package se.scmv.morocco.domain.usecases

import kotlinx.coroutines.flow.firstOrNull
import se.scmv.morocco.domain.coroutines.executeInParallel
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.CategoryTree
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.failure
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.domain.models.success
import se.scmv.morocco.domain.repositories.AccountRepository
import se.scmv.morocco.domain.repositories.AdRepository
import se.scmv.morocco.domain.repositories.ConfigRepository
import javax.inject.Inject

class GetAdInsertInitialDataUseCase @Inject constructor(
    private val configRepository: ConfigRepository,
    private val accountRepository: AccountRepository,
    private val adRepository: AdRepository
) {
    companion object {
        const val DEFAULT_CATEGORY_ID = "1010"
        val DEFAULT_AD_TYPE_KEY = AdTypeKey.SELL
    }

    data class Result(
        val adEditValues: List<OrionBaseComponentValue>,
        val categories: List<CategoryTree>,
        val selectedCategoryId: String,
        val selectedAdTypeKey: AdTypeKey,
    )

    suspend operator fun invoke(
        adId: String?,
    ): Resource<Result, NetworkAndBackendErrors> {
        val account: Account.Connected = accountRepository.currentAccount.firstOrNull()?.let {
            it as? Account.Connected
        } ?: return failure(NetworkAndBackendErrors.Network(NetworkErrors.UNKNOWN))
        val result = executeInParallel(
            firstCall = {
                if (adId != null) {
                    adRepository.getAdForEdit(adID = adId)
                } else success(null)
            },
            secondCall = {
                configRepository.getAdInsertCategories()
            }
        )
        return when (result) {
            is Resource.Success -> {
                val (adEditResult, categories) = result.data
                val categoryValue = adEditResult
                    ?.filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
                    ?.firstOrNull()
                val categoryId = categoryValue?.categoryId
                val adTypeKey = categoryValue?.adTypeKey
                if (adEditResult != null && categoryId != null && adTypeKey != null) {
                    success(
                        Result(
                            adEditValues = adEditResult,
                            categories = categories,
                            selectedCategoryId = categoryId,
                            selectedAdTypeKey = adTypeKey,
                        )
                    )
                } else when (account) {
                    is Account.Connected.Private -> success(
                        Result(
                            adEditValues = emptyList(),
                            categories = categories,
                            selectedCategoryId = DEFAULT_CATEGORY_ID,
                            selectedAdTypeKey = DEFAULT_AD_TYPE_KEY,
                        )
                    )

                    is Account.Connected.Shop -> {
                        val trees = CategoryTree.findTree(
                            trees = categories,
                            categoryId = account.store.category.id,
                            adTypeKey = null
                        )
                        val defaultResult = Result(
                            adEditValues = emptyList(),
                            categories = categories,
                            selectedCategoryId = DEFAULT_CATEGORY_ID,
                            selectedAdTypeKey = DEFAULT_AD_TYPE_KEY,
                        )
                        if (trees == null) return success(defaultResult)

                        val (accountCategoryId, accountAdTypeKey) = getCategoryIdAndAdType(trees)
                        if (accountAdTypeKey == null) return success(defaultResult)

                        success(
                            defaultResult.copy(
                                categories = listOf(trees),
                                selectedCategoryId = accountCategoryId,
                                selectedAdTypeKey = accountAdTypeKey,
                            )
                        )
                    }
                }
            }

            is Resource.Failure -> result
        }
    }

    private fun getCategoryIdAndAdType(category: CategoryTree): Pair<String, AdTypeKey?> {
        val childCategories = category.children
        return if (childCategories.isEmpty()) {
            Pair(
                category.category.id,
                category.adTypes.firstOrNull()?.key
            )
        } else {
            getCategoryIdAndAdType(childCategories.first())
        }
    }
}