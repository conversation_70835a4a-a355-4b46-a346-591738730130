<?xml version="1.0" encoding="utf-8"?>
<androidx.cardview.widget.CardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_marginBottom="17dp"
    app:cardElevation="0dp"
    app:cardCornerRadius="4dp">

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:background="@drawable/package_offer_background">

        <View
            android:id="@+id/side_decoration_view"
            android:layout_width="3dp"
            android:layout_height="0dp"
            android:background="@color/colorPrimary"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <ImageView
            android:id="@+id/packImage"
            android:layout_width="82dp"
            android:layout_height="82dp"
            android:layout_marginStart="12dp"
            android:layout_marginTop="12dp"
            android:layout_marginBottom="16dp"
            android:contentDescription="TODO"
            android:scaleType="fitXY"
            app:layout_constraintBottom_toTopOf="@+id/listContainer"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@+id/packBenefits"
            app:layout_constraintVertical_bias="0.0"
            tools:srcCompat="@tools:sample/avatars" />

        <TextView
            android:id="@+id/packTitle"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginEnd="8dp"
            android:fontFamily="@font/rubik_medium"
            android:maxEms="14"
            android:maxLines="2"
            android:paddingStart="0dp"
            android:paddingEnd="4dp"
            android:textAllCaps="true"
            android:textColor="#2d2d2d"
            android:textSize="16sp"
            app:layout_constraintBottom_toBottomOf="@+id/packImage"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toEndOf="@id/packImage"
            app:layout_constraintTop_toTopOf="@+id/packImage"
            app:layout_constraintVertical_bias="0.0"
            tools:text="Annonce Premium + Renouvellement" />

        <TextView
            android:id="@+id/packBenefits"
            android:layout_width="wrap_content"
            android:layout_height="26dp"
            android:background="@drawable/background_curved"
            android:fontFamily="@font/rubik_medium"
            android:gravity="center"
            android:paddingStart="10dp"
            android:paddingTop="3dp"
            android:paddingEnd="10dp"
            android:paddingBottom="3dp"
            android:textColor="@color/white"
            android:textSize="12sp"
            android:visibility="visible"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="15x plus de vues" />


        <TextView
            android:id="@+id/bestSellerBadge"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="8dp"
            android:layout_marginEnd="8dp"
            android:background="@drawable/best_seller_badge_bg"
            android:fontFamily="@font/rubik_medium"
            android:gravity="center"
            android:paddingStart="6dp"
            android:paddingTop="2dp"
            android:paddingEnd="6dp"
            android:paddingBottom="2dp"
            android:drawablePadding="4dp"
            android:textColor="@color/colorAccent"
            android:textAllCaps="true"
            android:textSize="10sp"
            android:visibility="gone"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            android:text="@string/best_seller_badge_text"
            app:drawableStartCompat="@drawable/ic_best_seller" />

        <TextView
            android:id="@+id/packPitch"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginTop="4dp"
            android:layout_marginEnd="12dp"
            android:layout_marginBottom="8dp"
            android:fontFamily="@font/rubik"
            android:text="Votre annonce sera en premiére position automatiquement chaque jour et en ajoutant un arrière-plan unique a votre annonce ."
            android:textColor="#2d2d2d"
            android:textSize="14sp"
            app:layout_constraintBottom_toTopOf="@+id/moreInfo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toEndOf="@+id/packImage"
            app:layout_constraintTop_toBottomOf="@+id/packTitle" />

        <TextView
            android:id="@+id/noImageWarning"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginStart="12dp"
            android:layout_marginBottom="8dp"
            android:clickable="false"
            android:drawablePadding="4dp"
            android:text="@string/atleastImage"
            android:textColor="@color/orange_red"
            android:textSize="12sp"
            android:visibility="gone"
            app:drawableStartCompat="@drawable/ic_error_warning_fill"
            app:drawableTint="@color/orange_red"
            app:layout_constraintBottom_toTopOf="@+id/listContainer"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintHorizontal_bias="0.0"
            app:layout_constraintStart_toStartOf="parent" />

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/listContainer"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:layout_marginTop="10dp"
            android:layout_marginEnd="12dp"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent">

            <LinearLayout
                android:id="@+id/radioGroup"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:listitem="@layout/package_offer_duration" />

            <View
                android:id="@+id/whiteOverlay"
                android:layout_width="match_parent"
                android:layout_height="match_parent"
                android:background="#CCFFFFFF"
                android:clickable="true"
                android:focusable="true"
                android:minHeight="50dp"
                android:layout_marginBottom="1dp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <com.google.android.material.button.MaterialButton
                android:id="@+id/addImages"
                style="@style/OrionButtonIconPrimary"
                android:insetBottom="0dp"
                android:insetTop="0dp"
                android:layout_width="wrap_content"
                android:layout_height="40dp"
                android:layout_gravity="center"
                android:drawablePadding="8dp"
                android:text="@string/choose_vas_package_screen_no_image_button"
                android:textColor="@color/colorPrimary"
                android:visibility="gone"
                app:backgroundTint="@color/appBackground"
                app:icon="@drawable/ic_camera_line"
                app:iconTint="@color/colorPrimary"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />
        </androidx.constraintlayout.widget.ConstraintLayout>


        <TextView
            android:id="@+id/moreInfo"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="8dp"
            android:layout_marginBottom="10dp"
            android:fontFamily="@font/rubik_medium"
            android:drawablePadding="4dp"

            app:drawableStartCompat="@drawable/info_ic"
            android:gravity="center_horizontal"
            android:text="@string/more_info"
            android:textColor="#a5a5a5"
            android:textSize="12sp"
            app:layout_constraintBottom_toTopOf="@+id/noImageWarning"
            app:layout_constraintStart_toEndOf="@+id/packImage" />

    </androidx.constraintlayout.widget.ConstraintLayout>

</androidx.cardview.widget.CardView>