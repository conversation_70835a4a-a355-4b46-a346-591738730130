<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:card_view="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/item_list_ads_background"
    android:backgroundTint="@color/white"
    android:padding="13dp"
    android:layout_margin="5dp">

    <ImageView
        android:id="@+id/vas_ai_package_image"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginStart="12dp"
        android:layout_marginTop="16dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:srcCompat="@tools:sample/avatars" />

    <TextView
        android:id="@+id/vas_ai_package_title"
        app:layout_constraintStart_toEndOf="@id/vas_ai_package_image"
        android:layout_marginTop="22dp"
        android:layout_marginStart="12dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toTopOf="@id/vas_ai_package_duration"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:fontFamily="@font/rubik_medium"
        android:maxLines="2"
        android:ems="14"
        android:layout_marginEnd="16dp"
        android:text="Renouvelement + Annonce Star" />

    <TextView
        app:layout_constraintTop_toBottomOf="@id/vas_ai_package_title"
        app:layout_constraintStart_toEndOf="@id/vas_ai_package_image"
        android:id="@+id/vas_ai_package_duration"
        android:fontFamily="@font/rubik"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginStart="12dp"
        android:text="15 Jours" />

    <TextView
        android:id="@+id/vas_ai_package_price"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_marginTop="32dp"
        android:layout_marginEnd="4dp"
        android:fontFamily="@font/rubik_medium"
        android:text="179DH"
        android:textColor="@color/orionBlue"
        android:textSize="16sp"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/packImage"
        app:layout_constraintTop_toTopOf="parent" />

    <View
        app:layout_constraintTop_toBottomOf="@id/vas_ai_package_price"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintBottom_toTopOf="@id/applyonAd"
        android:layout_marginTop="22dp"
        android:id="@+id/separator"
        android:layout_width="match_parent"
        android:layout_height="5dp"
        android:background="@drawable/dotted_separator" />

    <TextView
        android:id="@+id/applyonAd"
        app:layout_constraintTop_toBottomOf="@id/separator"
        app:layout_constraintStart_toStartOf="parent"
        android:layout_marginTop="10dp"
        android:layout_marginStart="12dp"
        android:textColor="@color/black_grey"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:text="Appliquer sur l'annonce: "
        android:visibility="gone"/>

    <androidx.cardview.widget.CardView
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@id/applyonAd"
        android:id="@+id/ad_listview"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_gravity="center_horizontal"
        android:layout_marginEnd="@dimen/margin_small_plus"
        android:layout_marginStart="@dimen/margin_small_plus"
        android:layout_marginTop="@dimen/margin_small_plus"
        android:layout_marginBottom="10dp"
        android:clickable="true"
        android:focusable="true"
        card_view:cardCornerRadius="12dp"
        card_view:cardElevation="0dp"
        tools:ignore="SmallSp"
        android:visibility="gone">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/list_card_container"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/item_ad_preview_background"
            android:baselineAligned="false"
            android:orientation="horizontal">

            <ImageView
                android:id="@+id/ad_thumb_image_view"
                android:layout_width="106dp"
                android:layout_height="0dp"
                android:adjustViewBounds="true"
                android:contentDescription="@null"
                android:scaleType="centerCrop"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintDimensionRatio="1.1"
                app:layout_constraintEnd_toStartOf="@+id/guideline5"
                app:layout_constraintHorizontal_bias="0.0"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:layout_constraintVertical_bias="0.0"
                tools:srcCompat="@tools:sample/backgrounds/scenic[4]" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/watched_flag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:layout_marginEnd="8dp"
                android:background="@drawable/watched_mention_bg"
                android:gravity="center"
                android:paddingStart="5dp"
                android:paddingEnd="5dp"
                android:text="@string/watched_ad"
                android:textColor="@color/white"
                android:textSize="8sp"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="@+id/ad_thumb_image_view"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.ConstraintLayout
                android:id="@+id/gallery_btn_container"
                android:layout_width="wrap_content"
                android:layout_height="24dp"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="8dp"
                android:background="@drawable/bg_rect_gray"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="@+id/ad_thumb_image_view">

                <androidx.appcompat.widget.AppCompatTextView
                    android:id="@+id/gallery_btn"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginStart="5dp"
                    android:layout_marginTop="5dp"
                    android:layout_marginEnd="5dp"
                    android:layout_marginBottom="5dp"
                    android:drawablePadding="5dp"
                    android:fontFamily="@font/rubik_medium"
                    android:gravity="center_vertical"
                    android:textColor="@android:color/white"
                    android:textSize="12sp"
                    app:drawableStartCompat="@drawable/ic_camera_card"
                    app:layout_constraintBottom_toBottomOf="parent"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:text="5"
                    android:visibility="gone" />

            </androidx.constraintlayout.widget.ConstraintLayout>


            <ImageView

                android:id="@+id/d2d_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp"
                android:contentDescription="@null"
                android:padding="@dimen/ad_cardview_btn_mrgn"
                android:src="@drawable/d2d_listing"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@+id/ad_price_view"
                app:layout_constraintStart_toEndOf="@+id/ad_price_view"
                app:layout_constraintTop_toTopOf="@+id/ad_price_view" />


            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/ad_title_view"
                style="@style/AdTitleText"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginEnd="40dp"
                android:fontFamily="@font/rubik_medium_light"
                android:gravity="top"
                android:textColor="#9b9b9b"
                android:textSize="12sp"
                android:maxLines="2"
                app:layout_constraintBottom_toTopOf="@+id/ad_location_view"
                app:layout_constraintEnd_toEndOf="parent"

                app:layout_constraintStart_toEndOf="@+id/ad_thumb_image_view"
                app:layout_constraintTop_toBottomOf="@+id/ad_price_view"
                tools:text="@tools:sample/lorem/random" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/ad_location_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginBottom="4dp"
                android:layout_marginStart="8dp"
                android:drawablePadding="@dimen/margin_tiny"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLength="14"
                android:maxLines="1"
                android:textColor="@color/card_detail_text_color"
                android:textSize="10sp"
                app:drawableStartCompat="@drawable/map_pin_fill"
                app:layout_constraintTop_toBottomOf="@id/ad_title_view"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/ad_thumb_image_view"
                tools:text="@tools:sample/cities" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/ad_date_view"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:drawablePadding="@dimen/margin_tiny"
                android:layout_marginBottom="4dp"
                android:layout_marginStart="8dp"
                android:ellipsize="end"
                android:gravity="center_vertical"
                android:maxLines="1"
                android:textColor="@color/card_detail_text_color"
                android:textSize="10sp"
                android:visibility="visible"
                app:drawableStartCompat="@drawable/time_fill"
                app:layout_constraintTop_toBottomOf="@id/ad_title_view"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toEndOf="@+id/ad_location_view"
                tools:text="@tools:sample/date/hhmm" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/ad_price_view"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="10dp"
                android:layout_marginTop="8dp"
                android:fontFamily="@font/rubik_medium_light"
                android:gravity="center"
                android:textColor="#9b9b9b"
                android:textSize="16sp"
                android:textStyle="bold"
                app:layout_constraintStart_toEndOf="@+id/ad_thumb_image_view"
                app:layout_constraintTop_toTopOf="parent"
                tools:text="@tools:sample/us_zipcodes" />


            <com.google.android.material.button.MaterialButton
                android:id="@+id/ad_status_button"
                style="@style/OrionButtonCoral"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@drawable/ad_status_pending_payment"
                android:fontFamily="@font/rubik_medium"
                android:gravity="center_horizontal|center_vertical"
                android:text="@string/continue_payement_btn_text"
                android:textSize="12sp"
                android:visibility="gone"
                app:icon="@drawable/ic_window_2_line"
                app:layout_constraintBottom_toBottomOf="@+id/ad_thumb_image_view"
                app:layout_constraintEnd_toEndOf="@+id/ad_thumb_image_view"
                app:layout_constraintStart_toStartOf="@+id/ad_thumb_image_view"
                app:layout_constraintTop_toTopOf="@+id/ad_thumb_image_view" />

            <androidx.appcompat.widget.AppCompatTextView
                android:id="@+id/ad_nc_tag"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginEnd="8dp"
                android:layout_marginBottom="8dp"
                android:gravity="center"
                android:paddingStart="4dp"
                android:paddingTop="2dp"
                android:paddingEnd="4dp"
                android:paddingBottom="2dp"
                android:textColor="@android:color/white"
                android:textSize="12sp"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent" />


            <ImageView
                android:id="@+id/delete_ad_button"
                android:layout_width="36dp"
                android:layout_height="36dp"
                android:background="@android:color/transparent"
                android:contentDescription="@null"
                android:padding="8dp"
                android:tint="#282828"
                android:visibility="gone"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                app:srcCompat="@drawable/ic_delete_ad" />

            <View
                android:id="@+id/transparency_filter"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:background="#4Dffffff"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />

            <androidx.constraintlayout.widget.Guideline
                android:id="@+id/guideline5"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                app:layout_constraintGuide_percent="0.38" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </androidx.cardview.widget.CardView>
</androidx.constraintlayout.widget.ConstraintLayout>