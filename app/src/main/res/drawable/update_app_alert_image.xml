<vector xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:aapt="http://schemas.android.com/aapt"
    android:width="258dp"
    android:height="214dp"
    android:viewportWidth="258"
    android:viewportHeight="214">
    <group>
        <clip-path android:pathData="M58.708,43.612C55.062,48.77 50.41,53.149 45.07,56.541L45.07,56.541C20.801,71.955 4.841,99.199 5.38,130.118L5.38,130.118C6.191,176.627 44.508,214 91.11,214L91.11,214C91.382,214 91.648,214 91.921,213.996L91.921,213.996C100.533,213.916 108.842,212.571 116.669,210.139L116.669,210.139C125.803,207.3 135.274,205.896 144.77,205.896L144.77,205.896C150.87,205.896 156.979,206.476 163.016,207.625L163.016,207.625C167.9,208.555 172.943,209.043 178.099,209.043L178.099,209.043L178.109,209.043C207.838,209.039 233.822,192.743 247.564,168.674L247.564,168.674C246.118,169.419 244.656,170.121 243.283,170.784L243.283,170.784C235.199,174.689 239.409,177.549 236.134,180.423L236.134,180.423C235.252,181.196 233.696,181.469 231.792,181.469L231.792,181.469C229.512,181.469 226.734,181.078 224.025,180.687L224.025,180.687C221.316,180.295 218.675,179.905 216.669,179.905L216.669,179.905C216.127,179.905 215.631,179.933 215.192,179.998L215.192,179.998C210.157,180.744 214.445,188.888 208.737,191.061L208.737,191.061C207.816,191.411 206.692,191.544 205.449,191.544L205.449,191.544C205.098,191.544 204.738,191.533 204.371,191.514L204.371,191.514L191.271,196.923C190.948,197.056 190.613,197.12 190.284,197.12L190.284,197.12C189.272,197.12 188.311,196.522 187.899,195.532L187.899,195.532L187.222,193.906C185.933,196.344 185.036,199.253 182.296,200.347L182.296,200.347C181.173,200.796 179.867,200.983 178.479,200.983L178.479,200.983C175.384,200.983 171.881,200.048 169.1,198.983L169.1,198.983C164.452,200.675 159.506,201.629 155.995,201.956L155.995,201.956C155.995,201.956 144.41,186.403 143.12,175.581L143.12,175.581C142.859,175.665 142.592,175.705 142.329,175.705L142.329,175.705C141.317,175.705 140.356,175.108 139.944,174.118L139.944,174.118L134.179,160.265C133.633,158.953 134.258,157.449 135.574,156.905L135.574,156.905L142.703,153.962C142.308,150.644 142.346,147.328 142.787,144.088L142.787,144.088L136.396,141.454C135.079,140.91 134.453,139.407 134.998,138.095L134.998,138.095L140.756,124.238C141.167,123.248 142.128,122.65 143.141,122.65L143.141,122.65C143.47,122.65 143.805,122.713 144.127,122.846L144.127,122.846L150.52,125.481C152.635,122.709 155.135,120.199 157.983,118.039L157.983,118.039L155.677,112.498C155.131,111.186 155.756,109.682 157.073,109.139L157.073,109.139L170.981,103.396C171.304,103.262 171.639,103.199 171.968,103.199L171.968,103.199C172.981,103.199 173.942,103.796 174.354,104.786L174.354,104.786L176.66,110.327C178.422,110.088 180.185,109.971 181.938,109.971L181.938,109.971C183.933,109.971 185.914,110.123 187.866,110.419L187.866,110.419L189.866,105.604C190.278,104.613 191.239,104.016 192.252,104.016L192.252,104.016C192.581,104.016 192.916,104.079 193.238,104.212L193.238,104.212L194.449,104.711C196.521,95.684 198.477,85.157 202.721,82.546L202.721,82.546C204.092,81.701 205.409,81.39 206.688,81.39L206.688,81.39C208.497,81.39 210.231,82.013 211.931,82.637L211.931,82.637C213.632,83.261 215.302,83.885 216.985,83.885L216.985,83.885C219.194,83.885 221.428,82.808 223.785,79.242L223.785,79.242C225.794,76.204 228.421,72.889 231.322,70.206L231.322,70.206C229.906,68.944 228.445,67.732 226.942,66.572L226.942,66.572C226.69,66.701 226.409,66.771 226.123,66.771L226.123,66.771C225.895,66.771 225.663,66.727 225.439,66.634L225.439,66.634L223.207,65.714C222.178,66.913 220.981,67.987 219.632,68.896L219.632,68.896L220.748,71.579C220.997,72.176 220.712,72.861 220.113,73.108L220.113,73.108L213.781,75.723C213.634,75.783 213.481,75.812 213.332,75.812L213.332,75.812C212.871,75.812 212.434,75.54 212.246,75.09L212.246,75.09L210.931,71.929C210.468,71.965 210.007,71.983 209.547,71.983L209.547,71.983C208.497,71.983 207.456,71.89 206.433,71.711L206.433,71.711L205.184,74.717C204.997,75.168 204.559,75.44 204.098,75.44L204.098,75.44C203.948,75.44 203.796,75.412 203.649,75.351L203.649,75.351L197.315,72.74C196.716,72.493 196.431,71.808 196.679,71.212L196.679,71.212L198.069,67.867C196.95,66.947 195.935,65.884 195.054,64.692L195.054,64.692L191.95,65.974C191.803,66.035 191.651,66.063 191.5,66.063L191.5,66.063C191.04,66.063 190.602,65.792 190.415,65.341L190.415,65.341L187.79,59.034C187.541,58.437 187.826,57.753 188.425,57.505L188.425,57.505L191.671,56.165C191.49,54.655 191.508,53.145 191.709,51.67L191.709,51.67L188.799,50.471C188.2,50.224 187.915,49.539 188.163,48.942L188.163,48.942L190.784,42.634C190.972,42.183 191.409,41.91 191.87,41.91L191.87,41.91C192.02,41.91 192.172,41.939 192.319,41.999L192.319,41.999L195.229,43.199C196.192,41.937 197.331,40.795 198.627,39.811L198.627,39.811L197.577,37.289C197.484,37.064 197.465,36.827 197.511,36.605L197.511,36.605C181.343,18.03 157.48,6.284 130.861,6.284L130.861,6.284C101.034,6.284 74.666,21.035 58.708,43.612M201.27,57.475C202.698,60.907 206.03,62.979 209.542,62.979L209.542,62.979C210.684,62.979 211.846,62.76 212.967,62.297L212.967,62.297C214.464,61.679 215.703,60.703 216.625,59.51L216.625,59.51C212.327,56.319 208.645,52.359 205.804,47.808L205.804,47.808C205.513,47.343 205.219,46.881 204.92,46.421L204.92,46.421C201.182,48.657 199.55,53.342 201.27,57.475M239.327,97.85C231.493,110.276 239.841,110.608 237.794,117.44L237.794,117.44C236.674,121.177 229.855,124.457 222.868,127.546L222.868,127.546L228.074,140.054C228.62,141.365 227.995,142.87 226.678,143.413L226.678,143.413L221.3,145.634C221.658,149.391 221.463,153.136 220.76,156.764L220.76,156.764L220.895,156.82C223.965,153.783 227.02,151.081 229.301,151.081L229.301,151.081L229.315,151.081C233.981,151.106 234.128,156.935 238.275,156.935L238.275,156.935C239.098,156.935 240.079,156.706 241.283,156.156L241.283,156.156C244.234,154.808 248.054,153.579 251.282,153.579L251.282,153.579C252.286,153.579 253.234,153.698 254.08,153.969L254.08,153.969C256.639,146.114 258.002,137.731 257.951,129.033L257.951,129.033C257.862,113.66 253.394,99.321 245.728,87.19L245.728,87.19C243.76,90.847 241.43,94.512 239.327,97.85M163.788,156.839C163.839,156.96 163.89,157.08 163.943,157.199L163.943,157.199C164.109,156.985 164.267,156.758 164.417,156.517L164.417,156.517C166.588,153.038 164.907,147.237 164.029,141.286L164.029,141.286C161.876,146.042 161.625,151.639 163.788,156.839M186.227,168.46C187.322,168.216 188.41,167.874 189.482,167.432L189.482,167.432C190.191,167.139 190.875,166.809 191.53,166.447L191.53,166.447C191.732,166.106 191.945,165.777 192.173,165.461L192.173,165.461C189.967,166.439 187.904,167.422 186.227,168.46 M 0,0" />
        <path
            android:pathData="M58.708,43.612C55.062,48.77 50.41,53.149 45.07,56.541L45.07,56.541C20.801,71.955 4.841,99.199 5.38,130.118L5.38,130.118C6.191,176.627 44.508,214 91.11,214L91.11,214C91.382,214 91.648,214 91.921,213.996L91.921,213.996C100.533,213.916 108.842,212.571 116.669,210.139L116.669,210.139C125.803,207.3 135.274,205.896 144.77,205.896L144.77,205.896C150.87,205.896 156.979,206.476 163.016,207.625L163.016,207.625C167.9,208.555 172.943,209.043 178.099,209.043L178.099,209.043L178.109,209.043C207.838,209.039 233.822,192.743 247.564,168.674L247.564,168.674C246.118,169.419 244.656,170.121 243.283,170.784L243.283,170.784C235.199,174.689 239.409,177.549 236.134,180.423L236.134,180.423C235.252,181.196 233.696,181.469 231.792,181.469L231.792,181.469C229.512,181.469 226.734,181.078 224.025,180.687L224.025,180.687C221.316,180.295 218.675,179.905 216.669,179.905L216.669,179.905C216.127,179.905 215.631,179.933 215.192,179.998L215.192,179.998C210.157,180.744 214.445,188.888 208.737,191.061L208.737,191.061C207.816,191.411 206.692,191.544 205.449,191.544L205.449,191.544C205.098,191.544 204.738,191.533 204.371,191.514L204.371,191.514L191.271,196.923C190.948,197.056 190.613,197.12 190.284,197.12L190.284,197.12C189.272,197.12 188.311,196.522 187.899,195.532L187.899,195.532L187.222,193.906C185.933,196.344 185.036,199.253 182.296,200.347L182.296,200.347C181.173,200.796 179.867,200.983 178.479,200.983L178.479,200.983C175.384,200.983 171.881,200.048 169.1,198.983L169.1,198.983C164.452,200.675 159.506,201.629 155.995,201.956L155.995,201.956C155.995,201.956 144.41,186.403 143.12,175.581L143.12,175.581C142.859,175.665 142.592,175.705 142.329,175.705L142.329,175.705C141.317,175.705 140.356,175.108 139.944,174.118L139.944,174.118L134.179,160.265C133.633,158.953 134.258,157.449 135.574,156.905L135.574,156.905L142.703,153.962C142.308,150.644 142.346,147.328 142.787,144.088L142.787,144.088L136.396,141.454C135.079,140.91 134.453,139.407 134.998,138.095L134.998,138.095L140.756,124.238C141.167,123.248 142.128,122.65 143.141,122.65L143.141,122.65C143.47,122.65 143.805,122.713 144.127,122.846L144.127,122.846L150.52,125.481C152.635,122.709 155.135,120.199 157.983,118.039L157.983,118.039L155.677,112.498C155.131,111.186 155.756,109.682 157.073,109.139L157.073,109.139L170.981,103.396C171.304,103.262 171.639,103.199 171.968,103.199L171.968,103.199C172.981,103.199 173.942,103.796 174.354,104.786L174.354,104.786L176.66,110.327C178.422,110.088 180.185,109.971 181.938,109.971L181.938,109.971C183.933,109.971 185.914,110.123 187.866,110.419L187.866,110.419L189.866,105.604C190.278,104.613 191.239,104.016 192.252,104.016L192.252,104.016C192.581,104.016 192.916,104.079 193.238,104.212L193.238,104.212L194.449,104.711C196.521,95.684 198.477,85.157 202.721,82.546L202.721,82.546C204.092,81.701 205.409,81.39 206.688,81.39L206.688,81.39C208.497,81.39 210.231,82.013 211.931,82.637L211.931,82.637C213.632,83.261 215.302,83.885 216.985,83.885L216.985,83.885C219.194,83.885 221.428,82.808 223.785,79.242L223.785,79.242C225.794,76.204 228.421,72.889 231.322,70.206L231.322,70.206C229.906,68.944 228.445,67.732 226.942,66.572L226.942,66.572C226.69,66.701 226.409,66.771 226.123,66.771L226.123,66.771C225.895,66.771 225.663,66.727 225.439,66.634L225.439,66.634L223.207,65.714C222.178,66.913 220.981,67.987 219.632,68.896L219.632,68.896L220.748,71.579C220.997,72.176 220.712,72.861 220.113,73.108L220.113,73.108L213.781,75.723C213.634,75.783 213.481,75.812 213.332,75.812L213.332,75.812C212.871,75.812 212.434,75.54 212.246,75.09L212.246,75.09L210.931,71.929C210.468,71.965 210.007,71.983 209.547,71.983L209.547,71.983C208.497,71.983 207.456,71.89 206.433,71.711L206.433,71.711L205.184,74.717C204.997,75.168 204.559,75.44 204.098,75.44L204.098,75.44C203.948,75.44 203.796,75.412 203.649,75.351L203.649,75.351L197.315,72.74C196.716,72.493 196.431,71.808 196.679,71.212L196.679,71.212L198.069,67.867C196.95,66.947 195.935,65.884 195.054,64.692L195.054,64.692L191.95,65.974C191.803,66.035 191.651,66.063 191.5,66.063L191.5,66.063C191.04,66.063 190.602,65.792 190.415,65.341L190.415,65.341L187.79,59.034C187.541,58.437 187.826,57.753 188.425,57.505L188.425,57.505L191.671,56.165C191.49,54.655 191.508,53.145 191.709,51.67L191.709,51.67L188.799,50.471C188.2,50.224 187.915,49.539 188.163,48.942L188.163,48.942L190.784,42.634C190.972,42.183 191.409,41.91 191.87,41.91L191.87,41.91C192.02,41.91 192.172,41.939 192.319,41.999L192.319,41.999L195.229,43.199C196.192,41.937 197.331,40.795 198.627,39.811L198.627,39.811L197.577,37.289C197.484,37.064 197.465,36.827 197.511,36.605L197.511,36.605C181.343,18.03 157.48,6.284 130.861,6.284L130.861,6.284C101.034,6.284 74.666,21.035 58.708,43.612M201.27,57.475C202.698,60.907 206.03,62.979 209.542,62.979L209.542,62.979C210.684,62.979 211.846,62.76 212.967,62.297L212.967,62.297C214.464,61.679 215.703,60.703 216.625,59.51L216.625,59.51C212.327,56.319 208.645,52.359 205.804,47.808L205.804,47.808C205.513,47.343 205.219,46.881 204.92,46.421L204.92,46.421C201.182,48.657 199.55,53.342 201.27,57.475M239.327,97.85C231.493,110.276 239.841,110.608 237.794,117.44L237.794,117.44C236.674,121.177 229.855,124.457 222.868,127.546L222.868,127.546L228.074,140.054C228.62,141.365 227.995,142.87 226.678,143.413L226.678,143.413L221.3,145.634C221.658,149.391 221.463,153.136 220.76,156.764L220.76,156.764L220.895,156.82C223.965,153.783 227.02,151.081 229.301,151.081L229.301,151.081L229.315,151.081C233.981,151.106 234.128,156.935 238.275,156.935L238.275,156.935C239.098,156.935 240.079,156.706 241.283,156.156L241.283,156.156C244.234,154.808 248.054,153.579 251.282,153.579L251.282,153.579C252.286,153.579 253.234,153.698 254.08,153.969L254.08,153.969C256.639,146.114 258.002,137.731 257.951,129.033L257.951,129.033C257.862,113.66 253.394,99.321 245.728,87.19L245.728,87.19C243.76,90.847 241.43,94.512 239.327,97.85M163.788,156.839C163.839,156.96 163.89,157.08 163.943,157.199L163.943,157.199C164.109,156.985 164.267,156.758 164.417,156.517L164.417,156.517C166.588,153.038 164.907,147.237 164.029,141.286L164.029,141.286C161.876,146.042 161.625,151.639 163.788,156.839M186.227,168.46C187.322,168.216 188.41,167.874 189.482,167.432L189.482,167.432C190.191,167.139 190.875,166.809 191.53,166.447L191.53,166.447C191.732,166.106 191.945,165.777 192.173,165.461L192.173,165.461C189.967,166.439 187.904,167.422 186.227,168.46"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="110.14194"
                    android:startX="4.8409185"
                    android:endY="110.14194"
                    android:endX="258.00183"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFF2F4" />
                    <item
                        android:offset="1"
                        android:color="#FFDBE2F6" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M229.338,151.269C227.06,151.269 224.009,153.95 220.943,156.962L220.943,156.962L225.899,158.991C227.214,159.529 227.839,161.021 227.295,162.322L227.295,162.322L222.059,174.838C221.434,176.334 219.972,177.237 218.433,177.237L218.433,177.237C217.933,177.237 217.424,177.142 216.934,176.941L216.934,176.941L212.036,174.935C209.779,177.549 207.153,179.889 204.193,181.869L204.193,181.869L206.642,187.716C207.188,189.016 206.564,190.509 205.249,191.048L205.249,191.048L204.441,191.379C204.808,191.399 205.167,191.409 205.517,191.409L205.517,191.409C206.759,191.409 207.882,191.277 208.801,190.93L208.801,190.93C214.502,188.775 210.22,180.695 215.248,179.955L215.248,179.955C215.686,179.891 216.182,179.863 216.723,179.863L216.723,179.863C218.727,179.863 221.364,180.25 224.07,180.638L224.07,180.638C226.775,181.026 229.549,181.415 231.826,181.415L231.826,181.415C233.728,181.415 235.282,181.144 236.162,180.377L236.162,180.377C239.434,177.526 235.229,174.689 243.303,170.815L243.303,170.815C244.673,170.157 246.133,169.461 247.578,168.722L247.578,168.722C253.34,165.776 258.863,162.158 257.535,157.64L257.535,157.64C256.977,155.744 255.721,154.656 254.085,154.134L254.085,154.134C253.24,153.865 252.294,153.747 251.291,153.747L251.291,153.747C248.067,153.747 244.252,154.966 241.305,156.303L241.305,156.303C240.102,156.849 239.123,157.076 238.301,157.076L238.301,157.076C234.159,157.076 234.012,151.293 229.353,151.269L229.353,151.269L229.338,151.269ZM192.259,165.534C192.032,165.847 191.819,166.174 191.617,166.512L191.617,166.512C192.659,165.938 193.631,165.28 194.529,164.551L194.529,164.551C193.762,164.879 193.002,165.206 192.259,165.534L192.259,165.534ZM182.069,188.595C181.996,188.595 181.923,188.595 181.85,188.594L181.85,188.594C181.574,189.639 181.123,190.646 180.42,191.597L180.42,191.597C178.075,194.773 173.79,197.136 169.216,198.789L169.216,198.789C171.994,199.846 175.492,200.774 178.583,200.774L178.583,200.774C179.969,200.774 181.273,200.587 182.395,200.142L182.395,200.142C185.131,199.057 186.027,196.171 187.314,193.753L187.314,193.753L185.104,188.478C184.091,188.556 183.078,188.595 182.069,188.595L182.069,188.595Z M 0,0" />
        <path
            android:pathData="M229.338,151.269C227.06,151.269 224.009,153.95 220.943,156.962L220.943,156.962L225.899,158.991C227.214,159.529 227.839,161.021 227.295,162.322L227.295,162.322L222.059,174.838C221.434,176.334 219.972,177.237 218.433,177.237L218.433,177.237C217.933,177.237 217.424,177.142 216.934,176.941L216.934,176.941L212.036,174.935C209.779,177.549 207.153,179.889 204.193,181.869L204.193,181.869L206.642,187.716C207.188,189.016 206.564,190.509 205.249,191.048L205.249,191.048L204.441,191.379C204.808,191.398 205.167,191.409 205.517,191.409L205.517,191.409C206.759,191.409 207.882,191.277 208.801,190.93L208.801,190.93C214.502,188.775 210.22,180.695 215.248,179.955L215.248,179.955C215.686,179.891 216.182,179.863 216.723,179.863L216.723,179.863C218.727,179.863 221.364,180.25 224.07,180.638L224.07,180.638C226.775,181.026 229.549,181.415 231.826,181.415L231.826,181.415C233.728,181.415 235.282,181.144 236.162,180.377L236.162,180.377C239.434,177.526 235.229,174.689 243.303,170.815L243.303,170.815C244.673,170.157 246.133,169.461 247.578,168.722L247.578,168.722C253.34,165.776 258.863,162.158 257.535,157.64L257.535,157.64C256.977,155.744 255.721,154.656 254.085,154.134L254.085,154.134C253.24,153.865 252.294,153.747 251.291,153.747L251.291,153.747C248.067,153.747 244.252,154.966 241.305,156.303L241.305,156.303C240.102,156.849 239.123,157.076 238.301,157.076L238.301,157.076C234.159,157.076 234.012,151.293 229.353,151.269L229.353,151.269L229.338,151.269ZM192.259,165.534C192.032,165.847 191.819,166.174 191.617,166.512L191.617,166.512C192.659,165.938 193.631,165.28 194.529,164.551L194.529,164.551C193.762,164.879 193.002,165.206 192.259,165.534L192.259,165.534ZM182.069,188.595C181.996,188.595 181.923,188.595 181.85,188.594L181.85,188.594C181.574,189.639 181.123,190.646 180.42,191.597L180.42,191.597C178.075,194.773 173.79,197.136 169.216,198.789L169.216,198.789C171.994,199.846 175.492,200.773 178.583,200.773L178.583,200.773C179.969,200.773 181.273,200.587 182.395,200.142L182.395,200.142C185.131,199.057 186.027,196.171 187.314,193.753L187.314,193.753L185.104,188.478C184.091,188.556 183.078,188.595 182.069,188.595L182.069,188.595Z"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="168.78725"
                    android:startX="238.26283"
                    android:endY="184.9669"
                    android:endX="177.72354"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFE2E4" />
                    <item
                        android:offset="1"
                        android:color="#FFA3B8E9" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M231.807,69.558C228.895,72.256 226.259,75.588 224.243,78.642L224.243,78.642C221.878,82.226 219.636,83.308 217.418,83.308L217.418,83.308C215.73,83.308 214.055,82.681 212.347,82.054L212.347,82.054C210.641,81.427 208.901,80.8 207.086,80.8L207.086,80.8C205.802,80.8 204.48,81.114 203.104,81.962L203.104,81.962C198.845,84.587 196.883,95.169 194.803,104.242L194.803,104.242L207.549,109.506C208.871,110.052 209.498,111.563 208.951,112.881L208.951,112.881L206.816,118.028C209.806,120.375 212.481,123.172 214.725,126.37L214.725,126.37L219.376,124.446C219.701,124.312 220.037,124.248 220.368,124.248L220.368,124.248C221.384,124.248 222.348,124.849 222.761,125.844L222.761,125.844L223.323,127.196C230.334,124.091 237.177,120.794 238.301,117.037L238.301,117.037C240.356,110.17 231.978,109.836 239.84,97.346L239.84,97.346C241.951,93.991 244.288,90.307 246.263,86.63L246.263,86.63C250.516,78.713 253.088,70.836 248.098,66.388L248.098,66.388C246.341,64.823 244.45,64.154 242.509,64.154L242.509,64.154C238.93,64.154 235.183,66.429 231.807,69.558L231.807,69.558ZM174.706,130.916C169.878,132.912 166.266,136.601 164.276,141.007L164.276,141.007C165.157,146.989 166.843,152.82 164.665,156.317L164.665,156.317C164.515,156.559 164.356,156.787 164.189,157.003L164.189,157.003C167.419,164.366 174.653,168.795 182.27,168.795L182.27,168.795C183.692,168.795 185.127,168.64 186.552,168.322L186.552,168.322C188.235,167.278 190.305,166.291 192.519,165.308L192.519,165.308C193.266,164.976 194.029,164.644 194.8,164.311L194.8,164.311C201.337,158.951 203.899,149.765 200.49,141.563L200.49,141.563C197.341,133.984 189.996,129.41 182.255,129.41L182.255,129.41C179.737,129.41 177.177,129.894 174.706,130.916L174.706,130.916ZM143.49,175.408C143.424,175.434 143.359,175.459 143.293,175.48L143.293,175.48C144.587,186.358 156.213,201.992 156.213,201.992L156.213,201.992C159.737,201.664 164.7,200.704 169.365,199.004L169.365,199.004C173.961,197.328 178.267,194.934 180.623,191.716L180.623,191.716C181.329,190.752 181.782,189.732 182.059,188.674L182.059,188.674C179.82,188.661 177.598,188.458 175.416,188.075L175.416,188.075L172.662,194.713C172.249,195.708 171.285,196.309 170.268,196.309L170.268,196.309C169.938,196.309 169.603,196.246 169.279,196.113L169.279,196.113L155.318,190.348C153.996,189.802 153.368,188.29 153.915,186.972L153.915,186.972L156.978,179.588C154.512,177.555 152.274,175.209 150.334,172.577L150.334,172.577L143.49,175.408Z M 0,0" />
        <path
            android:pathData="M231.807,69.558C228.895,72.256 226.259,75.588 224.243,78.642L224.243,78.642C221.878,82.226 219.636,83.308 217.418,83.308L217.418,83.308C215.73,83.308 214.055,82.681 212.347,82.054L212.347,82.054C210.641,81.427 208.901,80.8 207.086,80.8L207.086,80.8C205.802,80.8 204.48,81.114 203.104,81.962L203.104,81.962C198.845,84.587 196.883,95.169 194.803,104.242L194.803,104.242L207.549,109.506C208.871,110.052 209.498,111.563 208.951,112.881L208.951,112.881L206.816,118.028C209.806,120.375 212.481,123.172 214.725,126.37L214.725,126.37L219.376,124.446C219.701,124.312 220.037,124.248 220.368,124.248L220.368,124.248C221.384,124.248 222.348,124.849 222.761,125.844L222.761,125.844L223.323,127.196C230.334,124.091 237.177,120.794 238.301,117.037L238.301,117.037C240.356,110.17 231.978,109.836 239.84,97.346L239.84,97.346C241.951,93.991 244.288,90.307 246.263,86.63L246.263,86.63C250.516,78.713 253.088,70.836 248.098,66.388L248.098,66.388C246.341,64.823 244.45,64.154 242.509,64.154L242.509,64.154C238.93,64.154 235.183,66.429 231.807,69.558L231.807,69.558ZM174.706,130.916C169.878,132.912 166.266,136.601 164.276,141.007L164.276,141.007C165.157,146.989 166.843,152.82 164.665,156.317L164.665,156.317C164.515,156.559 164.356,156.787 164.189,157.003L164.189,157.003C167.419,164.366 174.653,168.795 182.27,168.795L182.27,168.795C183.692,168.795 185.127,168.64 186.552,168.322L186.552,168.322C188.235,167.278 190.305,166.291 192.519,165.308L192.519,165.308C193.266,164.976 194.029,164.644 194.8,164.311L194.8,164.311C201.337,158.951 203.899,149.765 200.49,141.563L200.49,141.563C197.341,133.984 189.996,129.41 182.255,129.41L182.255,129.41C179.737,129.41 177.177,129.894 174.706,130.916L174.706,130.916ZM143.49,175.408C143.424,175.434 143.359,175.459 143.293,175.48L143.293,175.48C144.587,186.358 156.213,201.992 156.213,201.992L156.213,201.992C159.737,201.664 164.7,200.704 169.365,199.004L169.365,199.004C173.961,197.328 178.267,194.934 180.623,191.716L180.623,191.716C181.329,190.752 181.782,189.732 182.059,188.674L182.059,188.674C179.82,188.661 177.598,188.458 175.416,188.075L175.416,188.075L172.662,194.713C172.249,195.708 171.285,196.309 170.268,196.309L170.268,196.309C169.938,196.309 169.603,196.246 169.279,196.113L169.279,196.113L155.318,190.348C153.996,189.802 153.368,188.29 153.915,186.972L153.915,186.972L156.978,179.588C154.512,177.555 152.274,175.209 150.334,172.577L150.334,172.577L143.49,175.408Z"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="140.46454"
                    android:startX="224.26854"
                    android:endY="125.78297"
                    android:endX="175.77684"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFE2E4" />
                    <item
                        android:offset="1"
                        android:color="#FFA3B8E9" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M19.526,70.464C18.864,70.653 18.182,70.907 17.475,71.232L17.475,71.232L17.467,71.235L17.467,71.236C17.094,71.407 16.714,71.599 16.327,71.811L16.327,71.811C-4.932,83.482 33.206,132.073 24.094,133.583L24.094,133.583C20.862,134.12 17.341,133.023 14.006,131.926L14.006,131.926C7.937,129.931 2.484,127.936 0.512,135.778L0.512,135.778C0.167,137.149 0.003,138.486 0,139.788L0,139.788L0,139.87C0.06,161.278 43.643,173.309 43.437,177.756L43.437,177.756C43.219,182.479 17.946,178.457 22.629,191.43L22.629,191.43C26.97,203.449 66.387,209.448 95.765,196.924L95.765,196.924C96.616,196.561 118.331,161.661 104,140.313L104,140.313C95.294,127.346 89.767,135.427 85.212,143.508L85.212,143.508C82.268,148.729 79.731,153.95 77.004,153.494L77.004,153.494C70.055,152.332 86.825,108.843 72.903,98.951L72.903,98.951C65.213,93.487 60.023,99.865 55.83,106.244L55.83,106.244C52.157,111.831 49.25,117.417 46.098,115.046L46.098,115.046C40.052,110.498 38.006,70.051 22.37,70.047L22.37,70.047C21.467,70.047 20.522,70.181 19.526,70.464 M 0,0" />
        <path
            android:pathData="M19.526,70.464C18.864,70.653 18.182,70.907 17.475,71.232L17.475,71.232L17.467,71.235L17.467,71.236C17.094,71.407 16.714,71.599 16.327,71.811L16.327,71.811C-4.932,83.482 33.206,132.073 24.094,133.583L24.094,133.583C20.862,134.12 17.341,133.023 14.006,131.926L14.006,131.926C7.937,129.931 2.484,127.936 0.512,135.778L0.512,135.778C0.167,137.149 0.003,138.486 0,139.788L0,139.788L0,139.87C0.06,161.278 43.643,173.309 43.437,177.756L43.437,177.756C43.219,182.479 17.946,178.457 22.629,191.43L22.629,191.43C26.97,203.449 66.387,209.448 95.765,196.924L95.765,196.924C96.616,196.561 118.331,161.661 104,140.313L104,140.313C95.294,127.346 89.767,135.427 85.212,143.508L85.212,143.508C82.268,148.729 79.731,153.95 77.004,153.494L77.004,153.494C70.055,152.332 86.825,108.843 72.903,98.951L72.903,98.951C65.213,93.487 60.023,99.865 55.83,106.244L55.83,106.244C52.157,111.831 49.25,117.417 46.098,115.046L46.098,115.046C40.052,110.498 38.006,70.051 22.37,70.047L22.37,70.047C21.467,70.047 20.522,70.181 19.526,70.464"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="80.57452"
                    android:startX="16.24849"
                    android:endY="206.96019"
                    android:endX="82.73367"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFE2E4" />
                    <item
                        android:offset="1"
                        android:color="#FFA3B8E9" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M171.302,103.357L157.361,109.136C156.042,109.682 155.415,111.196 155.962,112.516L155.962,112.516L158.273,118.091C155.419,120.265 152.913,122.791 150.793,125.58L150.793,125.58L144.386,122.928C144.062,122.794 143.727,122.731 143.397,122.731L143.397,122.731C142.382,122.731 141.419,123.333 141.006,124.329L141.006,124.329L135.235,138.273C134.688,139.593 135.316,141.106 136.636,141.653L136.636,141.653L143.042,144.303C142.6,147.564 142.562,150.901 142.958,154.239L142.958,154.239L135.813,157.201C134.493,157.748 133.867,159.261 134.414,160.581L134.414,160.581L140.193,174.522C140.605,175.518 141.569,176.119 142.583,176.119L142.583,176.119C142.847,176.119 143.114,176.078 143.376,175.993L143.376,175.993C143.074,173.448 143.335,171.163 144.441,169.399L144.441,169.399C150.06,160.43 159.888,163.147 164.247,157.497L164.247,157.497C164.194,157.377 164.143,157.256 164.092,157.134L164.092,157.134C161.924,151.902 162.176,146.27 164.333,141.484L164.333,141.484C163.564,136.25 163.408,130.901 166.981,126.892L166.981,126.892C174.638,118.295 184.39,122.864 189.851,117.977L189.851,117.977C192.025,116.032 193.45,110.686 194.824,104.68L194.824,104.68L193.61,104.177C193.287,104.043 192.951,103.98 192.622,103.98L192.622,103.98C191.606,103.98 190.643,104.582 190.231,105.579L190.231,105.579L188.226,110.423C186.269,110.125 184.283,109.973 182.284,109.973L182.284,109.973C180.527,109.973 178.76,110.091 176.993,110.331L176.993,110.331L174.682,104.755C174.269,103.76 173.306,103.159 172.291,103.159L172.291,103.159C171.961,103.159 171.626,103.222 171.302,103.357M208.714,135.13C202.349,140.558 215.446,148.218 209.39,156.228L209.39,156.228C207.607,158.586 204.241,160.525 200.477,162.301L200.477,162.301C202.374,162.478 204.004,163.189 205.518,163.9L205.518,163.9C207.267,164.721 208.863,165.542 210.548,165.542L210.548,165.542C210.83,165.542 211.114,165.519 211.403,165.469L211.403,165.469C213.818,165.051 217.585,160.834 221.331,157.115L221.331,157.115L221.196,157.059C221.901,153.409 222.096,149.639 221.737,145.859L221.737,145.859L227.127,143.624C228.447,143.078 229.074,141.564 228.527,140.244L228.527,140.244L223.309,127.658C217.511,130.231 211.598,132.671 208.714,135.13M189.846,167.793C188.771,168.239 187.68,168.582 186.582,168.828L186.582,168.828C184.897,169.875 183.598,170.978 182.931,172.19L182.931,172.19C181.995,173.889 181.776,175.781 181.832,177.756L181.832,177.756C182.414,177.874 182.986,177.95 183.552,177.95L183.552,177.95C184.327,177.95 185.091,177.807 185.852,177.433L185.852,177.433C188.702,176.035 189.572,170.723 191.898,166.802L191.898,166.802C191.241,167.167 190.556,167.499 189.846,167.793M190.3,191.274C189.061,191.965 188.265,193.133 187.58,194.434L187.58,194.434L188.258,196.07C188.671,197.066 189.634,197.667 190.649,197.667L190.649,197.667C190.979,197.667 191.315,197.603 191.639,197.469L191.639,197.469L204.769,192.026C203.071,191.937 201.215,191.661 199.392,191.386L199.392,191.386C197.175,191.05 195.007,190.715 193.235,190.715L193.235,190.715C192.042,190.715 191.028,190.867 190.3,191.274 M 0,0" />
        <path
            android:pathData="M171.302,103.357L157.361,109.136C156.042,109.682 155.415,111.196 155.962,112.516L155.962,112.516L158.273,118.091C155.419,120.265 152.913,122.791 150.793,125.58L150.793,125.58L144.386,122.928C144.062,122.794 143.727,122.731 143.397,122.731L143.397,122.731C142.382,122.731 141.419,123.333 141.006,124.329L141.006,124.329L135.235,138.273C134.688,139.593 135.316,141.106 136.636,141.653L136.636,141.653L143.042,144.303C142.6,147.564 142.562,150.901 142.958,154.239L142.958,154.239L135.813,157.201C134.493,157.748 133.867,159.261 134.414,160.581L134.414,160.581L140.193,174.522C140.605,175.518 141.569,176.119 142.583,176.119L142.583,176.119C142.847,176.119 143.114,176.078 143.376,175.993L143.376,175.993C143.074,173.448 143.335,171.163 144.441,169.399L144.441,169.399C150.06,160.43 159.888,163.147 164.247,157.497L164.247,157.497C164.194,157.377 164.143,157.256 164.092,157.134L164.092,157.134C161.924,151.902 162.176,146.27 164.333,141.484L164.333,141.484C163.564,136.25 163.408,130.901 166.981,126.892L166.981,126.892C174.638,118.295 184.39,122.864 189.851,117.977L189.851,117.977C192.025,116.032 193.45,110.686 194.824,104.68L194.824,104.68L193.61,104.177C193.287,104.043 192.951,103.98 192.622,103.98L192.622,103.98C191.606,103.98 190.643,104.582 190.231,105.579L190.231,105.579L188.226,110.423C186.269,110.125 184.283,109.973 182.284,109.973L182.284,109.973C180.527,109.973 178.76,110.091 176.993,110.331L176.993,110.331L174.682,104.755C174.269,103.76 173.306,103.159 172.291,103.159L172.291,103.159C171.961,103.159 171.626,103.222 171.302,103.357M208.714,135.13C202.349,140.558 215.446,148.218 209.389,156.228L209.389,156.228C207.607,158.586 204.241,160.525 200.477,162.301L200.477,162.301C202.374,162.478 204.004,163.189 205.518,163.9L205.518,163.9C207.267,164.721 208.863,165.542 210.548,165.542L210.548,165.542C210.83,165.542 211.114,165.519 211.403,165.469L211.403,165.469C213.818,165.051 217.585,160.834 221.331,157.115L221.331,157.115L221.196,157.059C221.901,153.409 222.096,149.639 221.737,145.859L221.737,145.859L227.127,143.624C228.447,143.078 229.074,141.564 228.527,140.244L228.527,140.244L223.309,127.658C217.511,130.231 211.598,132.671 208.714,135.13M189.846,167.793C188.771,168.239 187.68,168.582 186.582,168.828L186.582,168.828C184.897,169.875 183.598,170.978 182.931,172.19L182.931,172.19C181.995,173.889 181.776,175.781 181.832,177.756L181.832,177.756C182.414,177.874 182.986,177.95 183.552,177.95L183.552,177.95C184.327,177.95 185.091,177.807 185.852,177.433L185.852,177.433C188.702,176.035 189.572,170.723 191.898,166.802L191.898,166.802C191.241,167.167 190.556,167.499 189.846,167.793M190.3,191.274C189.061,191.965 188.265,193.133 187.58,194.434L187.58,194.434L188.258,196.07C188.671,197.066 189.634,197.667 190.649,197.667L190.649,197.667C190.979,197.667 191.315,197.603 191.639,197.469L191.639,197.469L204.769,192.026C203.071,191.937 201.215,191.661 199.392,191.386L199.392,191.386C197.175,191.05 195.007,190.715 193.235,190.715L193.235,190.715C192.042,190.715 191.028,190.867 190.3,191.274"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="150.41283"
                    android:startX="4.00222"
                    android:endY="150.41283"
                    android:endX="259.04312"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FF77A5E4" />
                    <item
                        android:offset="1"
                        android:color="#FF699EE6" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M211.36,164.759C211.075,164.809 210.794,164.832 210.516,164.832L210.516,164.832C208.851,164.832 207.273,164.008 205.546,163.184L205.546,163.184C204.049,162.47 202.439,161.756 200.564,161.578L200.564,161.578C198.749,162.448 196.842,163.279 194.975,164.1L194.975,164.1C194.085,164.843 193.121,165.513 192.087,166.097L192.087,166.097C189.789,170.033 188.929,175.367 186.113,176.771L186.113,176.771C185.361,177.146 184.607,177.29 183.841,177.29L183.841,177.29C183.282,177.29 182.717,177.213 182.141,177.095L182.141,177.095C182.247,180.871 183.337,184.951 182.401,188.586L182.401,188.586C182.474,188.587 182.546,188.587 182.619,188.587L182.619,188.587C183.62,188.587 184.624,188.547 185.629,188.468L185.629,188.468L187.821,193.84C188.498,192.534 189.284,191.361 190.508,190.667L190.508,190.667C191.228,190.259 192.23,190.106 193.409,190.106L193.409,190.106C195.16,190.106 197.301,190.442 199.492,190.779L199.492,190.779C201.293,191.056 203.127,191.332 204.805,191.422L204.805,191.422L205.606,191.085C206.91,190.536 207.529,189.016 206.988,187.691L206.988,187.691L204.559,181.737C207.494,179.72 210.099,177.337 212.337,174.676L212.337,174.676L217.194,176.718C217.68,176.923 218.184,177.02 218.68,177.02L218.68,177.02C220.207,177.02 221.657,176.1 222.277,174.577L222.277,174.577L227.469,161.831C228.009,160.506 227.389,158.986 226.084,158.438L226.084,158.438L221.17,156.371C217.469,160.106 213.747,164.339 211.36,164.759 M 0,0" />
        <path
            android:pathData="M211.36,164.759C211.075,164.809 210.794,164.832 210.516,164.832L210.516,164.832C208.851,164.832 207.273,164.008 205.546,163.184L205.546,163.184C204.049,162.47 202.439,161.756 200.564,161.578L200.564,161.578C198.749,162.448 196.842,163.279 194.975,164.1L194.975,164.1C194.085,164.843 193.121,165.513 192.087,166.097L192.087,166.097C189.789,170.033 188.929,175.367 186.113,176.771L186.113,176.771C185.361,177.146 184.607,177.29 183.841,177.29L183.841,177.29C183.282,177.29 182.717,177.214 182.141,177.095L182.141,177.095C182.247,180.871 183.337,184.951 182.401,188.586L182.401,188.586C182.474,188.587 182.546,188.587 182.619,188.587L182.619,188.587C183.62,188.587 184.624,188.547 185.629,188.468L185.629,188.468L187.821,193.84C188.498,192.534 189.284,191.361 190.508,190.667L190.508,190.667C191.228,190.259 192.23,190.106 193.409,190.106L193.409,190.106C195.16,190.106 197.301,190.442 199.492,190.779L199.492,190.779C201.293,191.056 203.127,191.332 204.805,191.422L204.805,191.422L205.606,191.085C206.91,190.536 207.529,189.016 206.988,187.691L206.988,187.691L204.559,181.737C207.494,179.72 210.099,177.337 212.337,174.676L212.337,174.676L217.194,176.718C217.68,176.923 218.184,177.02 218.68,177.02L218.68,177.02C220.207,177.02 221.657,176.1 222.277,174.577L222.277,174.577L227.469,161.831C228.009,160.506 227.389,158.986 226.084,158.438L226.084,158.438L221.17,156.371C217.469,160.106 213.747,164.339 211.36,164.759"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="156.58115"
                    android:startX="234.02965"
                    android:endY="192.27144"
                    android:endX="174.29984"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FF779EDF" />
                    <item
                        android:offset="1"
                        android:color="#FF528EE1" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M189.628,117.412C184.183,122.28 174.461,117.728 166.826,126.293L166.826,126.293C163.264,130.287 163.419,135.616 164.186,140.829L164.186,140.829C166.169,136.435 169.765,132.757 174.573,130.766L174.573,130.766C177.034,129.746 179.583,129.264 182.09,129.264L182.09,129.264C189.799,129.264 197.114,133.825 200.249,141.384L200.249,141.384C203.643,149.564 201.092,158.725 194.583,164.07L194.583,164.07C196.466,163.255 198.391,162.431 200.222,161.567L200.222,161.567C203.975,159.798 207.331,157.866 209.108,155.518L209.108,155.518C215.147,147.538 202.089,139.907 208.435,134.5L208.435,134.5C211.31,132.05 217.206,129.619 222.986,127.056L222.986,127.056L222.427,125.707C222.015,124.715 221.055,124.116 220.043,124.116L220.043,124.116C219.714,124.116 219.379,124.18 219.056,124.314L219.056,124.314L214.425,126.232C212.189,123.043 209.526,120.254 206.549,117.913L206.549,117.913L208.675,112.781C209.22,111.466 208.594,109.959 207.278,109.414L207.278,109.414L194.586,104.165C193.216,110.149 191.795,115.475 189.628,117.412M144.353,168.639C143.251,170.396 142.99,172.673 143.291,175.208L143.291,175.208C143.357,175.187 143.423,175.163 143.488,175.136L143.488,175.136L150.303,172.313C152.235,174.938 154.464,177.278 156.92,179.305L156.92,179.305L153.869,186.668C153.325,187.984 153.95,189.491 155.266,190.035L155.266,190.035L169.169,195.784C169.491,195.917 169.825,195.981 170.154,195.981L170.154,195.981C171.166,195.981 172.127,195.381 172.538,194.388L172.538,194.388L175.28,187.769C177.453,188.151 179.666,188.353 181.895,188.366L181.895,188.366C182.84,184.759 181.74,180.711 181.633,176.964L181.633,176.964C181.577,174.997 181.795,173.112 182.728,171.419L182.728,171.419C183.394,170.212 184.689,169.113 186.369,168.07L186.369,168.07C184.951,168.387 183.522,168.541 182.106,168.541L182.106,168.541C174.52,168.541 167.317,164.125 164.1,156.782L164.1,156.782C159.754,162.411 149.956,159.703 144.353,168.639 M 0,0" />
        <path
            android:pathData="M189.628,117.412C184.183,122.28 174.461,117.728 166.826,126.293L166.826,126.293C163.264,130.287 163.419,135.616 164.186,140.829L164.186,140.829C166.169,136.435 169.765,132.757 174.573,130.766L174.573,130.766C177.034,129.746 179.583,129.264 182.09,129.264L182.09,129.264C189.799,129.264 197.114,133.825 200.249,141.384L200.249,141.384C203.643,149.564 201.092,158.725 194.583,164.07L194.583,164.07C196.466,163.255 198.391,162.431 200.222,161.567L200.222,161.567C203.975,159.798 207.331,157.866 209.108,155.518L209.108,155.518C215.147,147.538 202.089,139.907 208.435,134.5L208.435,134.5C211.31,132.05 217.206,129.619 222.986,127.056L222.986,127.056L222.427,125.707C222.015,124.715 221.055,124.116 220.043,124.116L220.043,124.116C219.714,124.116 219.379,124.18 219.056,124.314L219.056,124.314L214.425,126.232C212.189,123.043 209.526,120.254 206.549,117.913L206.549,117.913L208.675,112.781C209.22,111.466 208.594,109.959 207.278,109.414L207.278,109.414L194.586,104.165C193.216,110.149 191.795,115.475 189.628,117.412M144.353,168.639C143.251,170.396 142.99,172.673 143.291,175.208L143.291,175.208C143.357,175.187 143.423,175.163 143.488,175.136L143.488,175.136L150.303,172.313C152.235,174.938 154.464,177.278 156.92,179.305L156.92,179.305L153.869,186.668C153.325,187.984 153.95,189.491 155.266,190.035L155.266,190.035L169.169,195.784C169.491,195.917 169.825,195.981 170.154,195.981L170.154,195.981C171.166,195.981 172.127,195.381 172.538,194.388L172.538,194.388L175.28,187.769C177.453,188.151 179.666,188.353 181.895,188.366L181.895,188.366C182.84,184.759 181.74,180.711 181.633,176.964L181.633,176.964C181.577,174.997 181.795,173.112 182.728,171.419L182.728,171.419C183.394,170.212 184.689,169.113 186.369,168.07L186.369,168.07C184.951,168.387 183.522,168.541 182.106,168.541L182.106,168.541C174.52,168.541 167.317,164.125 164.1,156.782L164.1,156.782C159.754,162.411 149.956,159.703 144.353,168.639"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="159.99475"
                    android:startX="222.2361"
                    android:endY="145.35318"
                    android:endX="163.38924"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FF779EDF" />
                    <item
                        android:offset="1"
                        android:color="#FF528EE1" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <path
        android:pathData="M205.457,33C205.307,33 205.155,33.029 205.009,33.091L198.699,35.743C198.326,35.9 198.076,36.229 198,36.601C200.68,39.735 203.15,43.064 205.384,46.558C205.758,46.33 206.154,46.127 206.57,45.953C207.687,45.483 208.844,45.261 209.982,45.261C213.482,45.261 216.803,47.362 218.227,50.844C219.495,53.946 218.935,57.354 217.049,59.836C218.088,60.622 219.164,61.361 220.272,62.052C222.715,63.575 225.07,65.227 227.329,67C227.691,66.811 227.992,66.496 228.16,66.084L230.539,60.258C230.786,59.652 230.502,58.958 229.905,58.707L227.592,57.737C227.911,56.061 228,54.332 227.837,52.597L230.277,51.571C230.874,51.32 231.158,50.626 230.91,50.02L228.295,43.622C228.108,43.165 227.672,42.889 227.212,42.889C227.063,42.889 226.911,42.918 226.765,42.98L224.662,43.864C223.647,42.395 222.438,41.11 221.086,40.032L222.052,37.667C222.299,37.062 222.015,36.367 221.418,36.116L215.106,33.468C214.96,33.406 214.808,33.377 214.659,33.377C214.199,33.377 213.763,33.653 213.576,34.111L212.669,36.334C211.783,36.197 210.885,36.128 209.98,36.128C209.184,36.128 208.384,36.181 207.585,36.292L206.539,33.733C206.352,33.276 205.916,33 205.457,33"
        android:strokeWidth="1"
        android:fillColor="#83B2EB"
        android:fillType="evenOdd"
        android:strokeColor="#00000000" />
    <group>
        <clip-path android:pathData="M197.281,36.833L198.339,39.387C197.032,40.384 195.884,41.541 194.912,42.819L194.912,42.819L191.976,41.604C191.828,41.542 191.674,41.514 191.523,41.514L191.523,41.514C191.058,41.514 190.617,41.789 190.427,42.246L190.427,42.246L187.783,48.635C187.533,49.24 187.821,49.933 188.425,50.184L188.425,50.184L191.36,51.398C191.158,52.892 191.14,54.421 191.322,55.951L191.322,55.951L188.048,57.308C187.443,57.559 187.156,58.252 187.407,58.857L187.407,58.857L190.055,65.245C190.244,65.701 190.685,65.976 191.15,65.976L191.15,65.976C191.302,65.976 191.455,65.947 191.604,65.886L191.604,65.886L194.736,64.587C195.624,65.795 196.648,66.871 197.777,67.803L197.777,67.803L196.375,71.19C196.125,71.795 196.412,72.488 197.017,72.739L197.017,72.739L203.406,75.383C203.554,75.445 203.708,75.474 203.859,75.474L203.859,75.474C204.324,75.474 204.766,75.198 204.955,74.741L204.955,74.741L206.215,71.696C207.246,71.878 208.297,71.971 209.356,71.971L209.356,71.971C209.821,71.971 210.286,71.954 210.752,71.917L210.752,71.917L212.079,75.118C212.269,75.575 212.71,75.85 213.174,75.85L213.174,75.85C213.326,75.85 213.48,75.821 213.628,75.759L213.628,75.759L220.016,73.111C220.621,72.861 220.907,72.168 220.657,71.563L220.657,71.563L219.53,68.845C220.892,67.925 222.099,66.837 223.138,65.622L223.138,65.622L225.389,66.555C225.615,66.648 225.849,66.693 226.079,66.693L226.079,66.693C226.368,66.693 226.651,66.622 226.905,66.492L226.905,66.492C224.618,64.722 222.234,63.072 219.761,61.552L219.761,61.552C218.639,60.862 217.55,60.123 216.498,59.339L216.498,59.339C215.566,60.547 214.317,61.536 212.806,62.162L212.806,62.162C211.676,62.63 210.504,62.853 209.352,62.853L209.352,62.853C205.809,62.853 202.447,60.754 201.006,57.278L201.006,57.278C199.271,53.091 200.917,48.347 204.689,46.082L204.689,46.082C202.428,42.593 199.928,39.27 197.214,36.14L197.214,36.14C197.168,36.365 197.186,36.605 197.281,36.833 M 0,0" />
        <path
            android:pathData="M197.281,36.833L198.339,39.387C197.032,40.384 195.884,41.541 194.912,42.819L194.912,42.819L191.976,41.604C191.828,41.542 191.674,41.514 191.523,41.514L191.523,41.514C191.058,41.514 190.617,41.789 190.427,42.246L190.427,42.246L187.783,48.635C187.533,49.24 187.821,49.933 188.425,50.184L188.425,50.184L191.36,51.398C191.158,52.892 191.14,54.421 191.322,55.951L191.322,55.951L188.048,57.308C187.443,57.559 187.156,58.252 187.407,58.857L187.407,58.857L190.055,65.245C190.244,65.701 190.685,65.976 191.15,65.976L191.15,65.976C191.302,65.976 191.455,65.947 191.604,65.886L191.604,65.886L194.736,64.587C195.624,65.795 196.648,66.871 197.777,67.803L197.777,67.803L196.375,71.19C196.125,71.795 196.412,72.488 197.017,72.739L197.017,72.739L203.406,75.383C203.554,75.445 203.708,75.474 203.859,75.474L203.859,75.474C204.324,75.474 204.766,75.198 204.955,74.741L204.955,74.741L206.215,71.696C207.246,71.878 208.297,71.971 209.356,71.971L209.356,71.971C209.821,71.971 210.286,71.954 210.752,71.917L210.752,71.917L212.079,75.118C212.269,75.575 212.71,75.85 213.174,75.85L213.174,75.85C213.326,75.85 213.48,75.821 213.628,75.759L213.628,75.759L220.016,73.111C220.621,72.861 220.907,72.168 220.657,71.563L220.657,71.563L219.53,68.845C220.892,67.925 222.099,66.837 223.138,65.622L223.138,65.622L225.389,66.555C225.615,66.648 225.849,66.693 226.079,66.693L226.079,66.693C226.368,66.693 226.651,66.622 226.905,66.492L226.905,66.492C224.618,64.722 222.234,63.072 219.761,61.552L219.761,61.552C218.639,60.862 217.55,60.123 216.498,59.339L216.498,59.339C215.566,60.547 214.317,61.536 212.806,62.162L212.806,62.162C211.676,62.63 210.504,62.853 209.352,62.853L209.352,62.853C205.809,62.853 202.447,60.754 201.006,57.278L201.006,57.278C199.271,53.091 200.917,48.347 204.689,46.082L204.689,46.082C202.428,42.593 199.928,39.27 197.214,36.14L197.214,36.14C197.168,36.365 197.186,36.605 197.281,36.833"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="55.995018"
                    android:startX="2.4663918"
                    android:endY="55.995018"
                    android:endX="258.3161"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FF83ACE6" />
                    <item
                        android:offset="1"
                        android:color="#FF72A5E8" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M60.275,21.42L56.041,23.213C55.64,23.383 55.45,23.852 55.616,24.262L55.616,24.262L56.318,25.991C55.451,26.666 54.69,27.449 54.046,28.315L54.046,28.315L52.1,27.492C51.698,27.322 51.239,27.517 51.073,27.927L51.073,27.927L49.32,32.253C49.154,32.662 49.345,33.131 49.746,33.301L49.746,33.301L51.691,34.123C51.557,35.135 51.546,36.17 51.666,37.206L51.666,37.206L49.496,38.125C49.095,38.294 48.904,38.764 49.071,39.174L49.071,39.174L50.826,43.498C50.992,43.907 51.452,44.102 51.853,43.932L51.853,43.932L53.929,43.053C54.518,43.871 55.197,44.599 55.945,45.231L55.945,45.231L55.016,47.524C54.849,47.933 55.04,48.403 55.441,48.572L55.441,48.572L59.676,50.363C60.077,50.532 60.537,50.338 60.703,49.928L60.703,49.928L61.538,47.866C62.522,48.043 63.532,48.096 64.546,48.016L64.546,48.016L65.426,50.183C65.592,50.593 66.052,50.787 66.452,50.617L66.452,50.617L70.687,48.825C71.088,48.655 71.278,48.185 71.112,47.776L71.112,47.776L70.365,45.936C71.268,45.313 72.068,44.577 72.756,43.754L72.756,43.754L74.249,44.385C74.859,44.643 75.559,44.347 75.811,43.723L75.811,43.723L77.407,39.785C77.573,39.375 77.382,38.906 76.982,38.736L76.982,38.736L75.43,38.081C75.644,36.948 75.703,35.779 75.594,34.606L75.594,34.606L77.232,33.913C77.633,33.743 77.823,33.273 77.657,32.864L77.657,32.864L75.901,28.539C75.735,28.13 75.275,27.936 74.875,28.105L74.875,28.105L73.464,28.702C72.783,27.709 71.971,26.841 71.064,26.112L71.064,26.112L71.712,24.514C71.878,24.104 71.687,23.635 71.286,23.465L71.286,23.465L67.051,21.675C66.65,21.505 66.191,21.7 66.025,22.109L66.025,22.109L65.415,23.613C64.299,23.439 63.151,23.425 62.004,23.584L62.004,23.584L61.302,21.854C61.177,21.545 60.884,21.358 60.576,21.358L60.576,21.358C60.475,21.358 60.374,21.378 60.275,21.42M58.085,38.104C56.819,34.985 58.269,31.408 61.323,30.114L61.323,30.114C64.377,28.821 67.879,30.302 69.145,33.422L69.145,33.422C70.411,36.541 68.962,40.118 65.908,41.411L65.908,41.411C65.158,41.728 64.382,41.878 63.617,41.878L63.617,41.878C61.269,41.878 59.041,40.458 58.085,38.104 M 0,0" />
        <path
            android:pathData="M60.275,21.42L56.041,23.213C55.64,23.383 55.45,23.852 55.616,24.262L55.616,24.262L56.318,25.991C55.451,26.666 54.69,27.449 54.046,28.315L54.046,28.315L52.1,27.492C51.698,27.322 51.239,27.517 51.073,27.927L51.073,27.927L49.32,32.253C49.154,32.662 49.345,33.131 49.746,33.301L49.746,33.301L51.691,34.123C51.557,35.135 51.546,36.17 51.666,37.206L51.666,37.206L49.496,38.125C49.095,38.294 48.904,38.764 49.071,39.174L49.071,39.174L50.826,43.498C50.992,43.907 51.452,44.102 51.853,43.932L51.853,43.932L53.929,43.053C54.518,43.871 55.197,44.599 55.945,45.231L55.945,45.231L55.016,47.524C54.849,47.933 55.04,48.403 55.441,48.572L55.441,48.572L59.676,50.363C60.077,50.532 60.537,50.338 60.703,49.928L60.703,49.928L61.538,47.866C62.522,48.043 63.532,48.096 64.546,48.016L64.546,48.016L65.426,50.183C65.592,50.593 66.052,50.787 66.452,50.617L66.452,50.617L70.687,48.825C71.088,48.655 71.278,48.185 71.112,47.776L71.112,47.776L70.365,45.936C71.268,45.313 72.068,44.577 72.756,43.754L72.756,43.754L74.249,44.385C74.859,44.643 75.559,44.347 75.811,43.723L75.811,43.723L77.407,39.785C77.573,39.375 77.382,38.906 76.982,38.736L76.982,38.736L75.43,38.081C75.644,36.948 75.703,35.779 75.594,34.606L75.594,34.606L77.232,33.913C77.633,33.743 77.823,33.273 77.657,32.864L77.657,32.864L75.901,28.539C75.735,28.13 75.275,27.936 74.875,28.105L74.875,28.105L73.464,28.702C72.783,27.709 71.971,26.841 71.064,26.112L71.064,26.112L71.712,24.514C71.878,24.104 71.687,23.635 71.286,23.465L71.286,23.465L67.051,21.675C66.65,21.505 66.191,21.7 66.025,22.109L66.025,22.109L65.415,23.613C64.299,23.439 63.151,23.425 62.004,23.584L62.004,23.584L61.302,21.854C61.177,21.545 60.884,21.358 60.576,21.358L60.576,21.358C60.475,21.358 60.374,21.378 60.275,21.42M58.085,38.104C56.819,34.985 58.269,31.408 61.323,30.114L61.323,30.114C64.377,28.821 67.879,30.302 69.145,33.422L69.145,33.422C70.411,36.541 68.962,40.118 65.908,41.411L65.908,41.411C65.158,41.728 64.382,41.878 63.617,41.878L63.617,41.878C61.269,41.878 59.041,40.458 58.085,38.104"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="7.7147794"
                    android:startX="63.57095"
                    android:endY="95.581436"
                    android:endX="62.92835"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFE2E4" />
                    <item
                        android:offset="1"
                        android:color="#FFA3B8E9" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M81.924,71.247C81.22,76.423 81.059,79.918 82.036,84.698L82.036,84.698C83.035,89.334 85.172,95.253 89.602,101.975L89.602,101.975C93.929,108.618 100.547,116.063 108.108,121.949L108.108,121.949C113.787,126.499 120.02,130.101 123.62,131.522L123.62,131.522C120.02,132.943 113.787,136.545 108.108,141.094L108.108,141.094C100.547,146.98 93.929,154.426 89.602,161.069L89.602,161.069C85.172,167.79 83.035,173.71 82.036,178.346L82.036,178.346C81.059,183.125 81.22,186.621 81.924,191.797L81.924,191.797C111.323,191.797 140.722,191.797 170.121,191.797L170.121,191.797C170.825,186.621 170.987,183.125 170.009,178.346L170.009,178.346C169.008,173.711 166.867,167.793 162.436,161.072L162.436,161.072C158.11,154.429 151.496,146.982 143.936,141.095L143.936,141.095C138.258,136.545 132.025,132.943 128.425,131.522L128.425,131.522C132.025,130.101 138.258,126.499 143.936,121.949L143.936,121.949C151.496,116.062 158.11,108.614 162.436,101.971L162.436,101.971C166.867,95.25 169.008,89.333 170.009,84.697L170.009,84.697C170.987,79.918 170.825,76.423 170.121,71.247L170.121,71.247C155.422,71.247 140.722,71.247 126.023,71.247L126.023,71.247C111.323,71.247 96.624,71.247 81.924,71.247 M 0,0" />
        <path
            android:pathData="M81.924,71.247C81.22,76.423 81.059,79.918 82.036,84.698L82.036,84.698C83.035,89.334 85.172,95.253 89.602,101.975L89.602,101.975C93.929,108.618 100.547,116.063 108.108,121.949L108.108,121.949C113.787,126.499 120.02,130.101 123.62,131.522L123.62,131.522C120.02,132.943 113.787,136.545 108.108,141.094L108.108,141.094C100.547,146.98 93.929,154.426 89.602,161.069L89.602,161.069C85.172,167.79 83.035,173.71 82.036,178.346L82.036,178.346C81.059,183.125 81.22,186.621 81.924,191.797L81.924,191.797C111.323,191.797 140.722,191.797 170.121,191.797L170.121,191.797C170.825,186.621 170.987,183.125 170.009,178.346L170.009,178.346C169.008,173.711 166.867,167.793 162.436,161.072L162.436,161.072C158.11,154.429 151.496,146.982 143.936,141.095L143.936,141.095C138.258,136.545 132.025,132.943 128.425,131.522L128.425,131.522C132.025,130.101 138.258,126.499 143.936,121.949L143.936,121.949C151.496,116.062 158.11,108.614 162.436,101.971L162.436,101.971C166.867,95.25 169.008,89.333 170.009,84.697L170.009,84.697C170.987,79.918 170.825,76.423 170.121,71.247L170.121,71.247C155.422,71.247 140.722,71.247 126.023,71.247L126.023,71.247C111.323,71.247 96.624,71.247 81.924,71.247"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="131.5219"
                    android:startX="81.058975"
                    android:endY="131.5219"
                    android:endX="170.98714"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFFBFB" />
                    <item
                        android:offset="1"
                        android:color="#FFFFE2E4" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M79.929,55.113C76.172,55.113 73.126,58.225 73.126,62.064L73.126,62.064L73.126,64.914C73.126,68.753 76.172,71.866 79.929,71.866L79.929,71.866L173.117,71.866C176.874,71.866 179.919,68.753 179.919,64.914L179.919,64.914L179.919,62.064C179.919,58.225 176.874,55.113 173.117,55.113L173.117,55.113L79.929,55.113Z M 0,0" />
        <path
            android:pathData="M79.929,55.113C76.172,55.113 73.126,58.225 73.126,62.064L73.126,62.064L73.126,64.914C73.126,68.753 76.172,71.866 79.929,71.866L79.929,71.866L173.117,71.866C176.874,71.866 179.919,68.753 179.919,64.914L179.919,64.914L179.919,62.064C179.919,58.225 176.874,55.113 173.117,55.113L173.117,55.113L79.929,55.113Z"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="63.489464"
                    android:startX="73.12636"
                    android:endY="63.489464"
                    android:endX="179.91885"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FF1C72DB" />
                    <item
                        android:offset="1"
                        android:color="#FF0E23A3" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M79.929,192.179C76.172,192.179 73.126,195.291 73.126,199.13L73.126,199.13L73.126,201.98C73.126,205.819 76.172,208.931 79.929,208.931L79.929,208.931L173.117,208.931C176.874,208.931 179.919,205.819 179.919,201.98L179.919,201.98L179.919,199.13C179.919,195.291 176.874,192.179 173.117,192.179L173.117,192.179L79.929,192.179Z M 0,0" />
        <path
            android:pathData="M79.929,192.179C76.172,192.179 73.126,195.291 73.126,199.13L73.126,199.13L73.126,201.98C73.126,205.819 76.172,208.931 79.929,208.931L79.929,208.931L173.117,208.931C176.874,208.931 179.919,205.819 179.919,201.98L179.919,201.98L179.919,199.13C179.919,195.291 176.874,192.179 173.117,192.179L173.117,192.179L79.929,192.179Z"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="200.55502"
                    android:startX="73.12636"
                    android:endY="200.55502"
                    android:endX="179.91885"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FF1C72DB" />
                    <item
                        android:offset="1"
                        android:color="#FF0E23A3" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M82.172,191.802C111.739,191.802 141.307,191.802 170.873,191.802L170.873,191.802C171.137,189.921 142.162,168.081 126.523,168.081L126.523,168.081C110.884,168.081 81.909,189.921 82.172,191.802 M 0,0" />
        <path
            android:pathData="M82.172,191.802C111.739,191.802 141.307,191.802 170.873,191.802L170.873,191.802C171.137,189.921 142.162,168.081 126.523,168.081L126.523,168.081C110.884,168.081 81.909,189.921 82.172,191.802"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="179.94154"
                    android:startX="81.90901"
                    android:endY="179.94154"
                    android:endX="171.137"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFC1C1" />
                    <item
                        android:offset="1"
                        android:color="#FFFD958E" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M108.498,122.04C114.212,126.604 120.484,130.217 124.106,131.643L124.106,131.643C124.009,131.681 123.902,131.727 123.8,131.768L123.8,131.768C125.615,131.833 127.431,131.833 129.245,131.768L129.245,131.768C129.144,131.727 129.037,131.681 128.94,131.643L128.94,131.643C132.562,130.217 138.834,126.604 144.547,122.039L144.547,122.039C149.073,118.525 153.259,114.455 156.826,110.332L156.826,110.332C138.512,119.083 114.523,119.082 96.209,110.328L96.209,110.328C99.78,114.453 103.968,118.524 108.498,122.04 M 0,0" />
        <path
            android:pathData="M108.498,122.04C114.212,126.604 120.484,130.217 124.106,131.643L124.106,131.643C124.009,131.681 123.902,131.727 123.8,131.768L123.8,131.768C125.615,131.833 127.431,131.833 129.245,131.768L129.245,131.768C129.144,131.727 129.037,131.681 128.94,131.643L128.94,131.643C132.562,130.217 138.834,126.604 144.547,122.039L144.547,122.039C149.073,118.525 153.259,114.455 156.826,110.332L156.826,110.332C138.512,119.083 114.523,119.082 96.209,110.328L96.209,110.328C99.78,114.453 103.968,118.524 108.498,122.04"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="121.080635"
                    android:startX="96.20927"
                    android:endY="121.080635"
                    android:endX="156.82599"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFC1C1" />
                    <item
                        android:offset="1"
                        android:color="#FFFD958E" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M169.407,37.041C169.33,37.041 169.268,37.103 169.268,37.181L169.268,37.181L169.268,45.579C169.268,45.657 169.33,45.719 169.407,45.719L169.407,45.719L173.825,45.719C173.902,45.719 173.964,45.657 173.964,45.579L173.964,45.579L173.964,37.181C173.964,37.103 173.902,37.041 173.825,37.041L173.825,37.041L169.407,37.041Z M 0,0" />
        <path
            android:pathData="M168.36,46.634l6.511,0l0,-10.508l-6.511,0z"
            android:strokeWidth="1"
            android:fillColor="#7B7C78"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M169.477,37.041C169.393,37.041 169.325,37.104 169.325,37.183L169.325,37.183L169.325,45.577C169.325,45.655 169.393,45.719 169.477,45.719L169.477,45.719L173.81,45.719C173.893,45.719 173.961,45.655 173.961,45.577L173.961,45.577L173.961,37.183C173.961,37.104 173.893,37.041 173.81,37.041L173.81,37.041L169.477,37.041Z M 0,0" />
        <path
            android:pathData="M168.348,46.634l6.591,0l0,-10.508l-6.591,0z"
            android:strokeWidth="1"
            android:fillColor="#D9D9D3"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M170.163,37.526C170.163,37.677 170.286,37.8 170.437,37.8L170.437,37.8C170.588,37.8 170.711,37.677 170.711,37.526L170.711,37.526C170.711,37.375 170.588,37.252 170.437,37.252L170.437,37.252C170.286,37.252 170.163,37.375 170.163,37.526L170.163,37.526Z M 0,0" />
        <path
            android:pathData="M169.316,38.647l2.242,0l0,-2.242l-2.242,0z"
            android:strokeWidth="1"
            android:fillColor="#1D1E1C"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M170.45,37.289C170.45,37.403 170.543,37.495 170.655,37.495L170.655,37.495C170.769,37.495 170.862,37.403 170.862,37.289L170.862,37.289C170.862,37.176 170.769,37.084 170.655,37.084L170.655,37.084C170.543,37.084 170.45,37.176 170.45,37.289L170.45,37.289Z M 0,0" />
        <path
            android:pathData="M169.18,38.765l2.952,0l0,-2.952l-2.952,0z"
            android:strokeWidth="1"
            android:fillColor="#484846"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M146.535,24.809C146.89,24.869 146.982,24.318 146.627,24.259L146.627,24.259C146.609,24.256 146.592,24.254 146.575,24.254L146.575,24.254C146.264,24.254 146.198,24.753 146.535,24.809L146.535,24.809Z M 0,0" />
        <path
            android:pathData="M145.463,25.661l2.236,0l0,-2.254l-2.236,0z"
            android:strokeWidth="1"
            android:fillColor="#FFFFFE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M147.035,28.274L147.007,28.384C146.969,28.534 147.126,28.68 147.35,28.705L147.35,28.705C147.576,28.73 147.795,28.626 147.833,28.476L147.833,28.476L147.86,28.366C147.898,28.216 147.741,28.07 147.516,28.045L147.516,28.045C147.494,28.042 147.472,28.041 147.449,28.041L147.449,28.041C147.25,28.041 147.068,28.139 147.035,28.274L147.035,28.274Z M 0,0" />
        <path
            android:pathData="M145.731,29.556l3.405,0l0,-2.362l-3.405,0z"
            android:strokeWidth="1"
            android:fillColor="#FFFFFE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M145.628,21.661L145.623,21.66L145.628,21.661Z M 0,0" />
        <path
            android:pathData="M144.352,22.932l2.546,0l0,-2.543l-2.546,0z"
            android:strokeWidth="1"
            android:fillColor="#508CAE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M161.698,29.207C161.225,29.214 159.881,30.237 159.302,30.439L159.302,30.439C158.915,30.574 155.188,32.087 155.111,31.3L155.111,31.3C155.199,32.203 155.398,33.153 155.543,34.014L155.543,34.014C155.769,35.348 155.973,36.658 156.288,37.978L156.288,37.978C156.85,40.33 158.605,44.01 161.335,43.96L161.335,43.96C164.396,43.904 163.757,41.15 163.379,38.993L163.379,38.993C162.831,35.867 163.156,33.658 162.64,31.15L162.64,31.15C162.482,30.381 162.582,29.207 161.71,29.207L161.71,29.207C161.706,29.207 161.702,29.207 161.698,29.207L161.698,29.207Z M 0,0" />
        <path
            android:pathData="M154.158,44.938l10.5,0l0,-16.708l-10.5,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M159.318,39.765C155.524,43.207 161.849,48.164 164.345,49.36L164.345,49.36C166.47,50.377 168.706,51.242 170.988,51.791L170.988,51.791C171.199,51.842 172.995,48.897 172.814,48.808L172.814,48.808C170.341,47.597 168.501,45.724 167.845,45.179L167.845,45.179C166,43.648 165.191,43.057 163.67,41.681L163.67,41.681C162.736,40.837 161.678,39.257 160.469,39.257L160.469,39.257C160.1,39.257 159.717,39.404 159.318,39.765L159.318,39.765Z M 0,0" />
        <path
            android:pathData="M157.157,52.764l16.647,0l0,-14.479l-16.647,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M176.776,48.232C176.165,48.255 175.648,48.507 175.025,48.449L175.025,48.449C174.411,48.39 173.646,48.171 173.07,48.696L173.07,48.696C173.035,48.727 172.999,48.766 172.963,48.81L172.963,48.81C172.605,49.239 172.078,50.36 172.525,50.784L172.525,50.784C173.027,51.26 174.363,50.463 174.902,50.182L174.902,50.182C175.326,49.961 175.834,49.729 176.216,49.415L176.216,49.415C176.343,49.31 177.14,48.232 176.783,48.232L176.783,48.232C176.78,48.232 176.778,48.232 176.776,48.232L176.776,48.232Z M 0,0" />
        <path
            android:pathData="M171.376,52.021l6.476,0l0,-4.879l-6.476,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M173.281,49.395C172.735,49.55 172.245,49.873 171.754,50.143L171.754,50.143C171.303,50.391 170.723,50.824 170.189,50.868L170.189,50.868L170.18,50.923C170.59,52.071 173.046,53.445 173.76,53.601L173.76,53.601C174.27,53.71 174.468,53.824 174.992,53.881L174.992,53.881C175.392,53.924 176.697,53.577 175.762,53.332L175.762,53.332C176.154,53.435 177.107,53.244 177.311,52.784L177.311,52.784C177.286,52.623 177.021,52.669 176.907,52.566L176.907,52.566C176.987,52.543 177.917,52.443 177.799,52.139L177.799,52.139C177.619,51.687 177.227,51.908 176.839,51.727L176.839,51.727C177.052,51.826 177.167,51.456 177.139,51.332L177.139,51.332C177.083,51.094 176.876,51.063 176.677,51.001L176.677,51.001C176.036,50.804 175.505,50.45 175.023,49.989L175.023,49.989C174.822,49.797 174.467,49.318 174.195,49.244L174.195,49.244C174.158,49.234 174.114,49.23 174.066,49.23L174.066,49.23C173.821,49.23 173.463,49.343 173.281,49.395L173.281,49.395Z M 0,0" />
        <path
            android:pathData="M169.256,54.792l9.477,0l0,-6.471l-9.477,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M157.259,40.031C157.788,43.03 161.823,46.795 161.823,46.795L161.823,46.795L159.618,36.207C159.618,36.207 156.731,37.032 157.259,40.031L157.259,40.031Z M 0,0" />
        <path
            android:pathData="M156.287,47.759l6.444,0l0,-12.516l-6.444,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <path
        android:pathData="M174.417,90.274C174.11,90.18 173.793,90.284 173.476,90.241C172.699,90.132 172.181,89.716 171.615,89.348C171.421,89.222 171.219,89.101 171,89C171.102,90.031 171.918,90.821 173.309,90.966C173.622,90.998 174.875,91.081 174.994,90.762C175.059,90.588 174.594,90.33 174.417,90.274"
        android:strokeWidth="1"
        android:fillColor="#121322"
        android:fillType="evenOdd"
        android:strokeColor="#00000000" />
    <group>
        <clip-path android:pathData="M173.404,91.818C173.681,91.862 174.791,91.975 174.896,91.54L174.896,91.54C174.954,91.304 174.543,90.952 174.385,90.876L174.385,90.876C174.23,90.803 174.071,90.818 173.911,90.833L173.911,90.833C173.792,90.845 173.672,90.856 173.553,90.83L173.553,90.83C172.865,90.683 172.407,90.116 171.906,89.615L171.906,89.615C171.733,89.443 171.555,89.279 171.361,89.141L171.361,89.141C171.452,90.545 172.174,91.62 173.404,91.818L173.404,91.818Z M 0,0" />
        <path
            android:pathData="M16.046,214.017l217.913,0l0,-225.676l-217.913,0z"
            android:strokeWidth="1"
            android:fillColor="#121322"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M173.404,91.818C173.681,91.862 174.791,91.975 174.896,91.54L174.896,91.54C174.954,91.304 174.543,90.952 174.385,90.876L174.385,90.876C174.23,90.803 174.071,90.818 173.911,90.833L173.911,90.833C173.792,90.845 173.672,90.856 173.553,90.83L173.553,90.83C172.865,90.683 172.407,90.116 171.906,89.615L171.906,89.615C171.733,89.443 171.555,89.279 171.361,89.141L171.361,89.141C171.452,90.545 172.174,91.62 173.404,91.818L173.404,91.818Z M 0,0" />
        <path
            android:pathData="M170.437,92.954l5.389,0l0,-4.902l-5.389,0z"
            android:strokeWidth="1"
            android:fillColor="#121322"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M170.913,78.602C169.904,79.445 170.218,81.088 170.284,82.137L170.284,82.137C170.33,82.905 170.019,86.481 170.536,87.191L170.536,87.191C170.901,87.694 171.472,88.049 171.977,88.411L171.977,88.411C172.215,88.582 172.331,88.759 172.523,88.959L172.523,88.959C172.769,89.216 173.139,89.318 173.455,89.472L173.455,89.472C173.727,89.605 174.153,89.906 174.483,89.873L174.483,89.873C174.939,89.829 174.605,89.41 174.465,89.215L174.465,89.215C174.196,88.842 173.907,88.513 173.755,88.072L173.755,88.072C173.269,86.652 173.429,84.743 173.896,83.323L173.896,83.323C174.329,82.008 174.453,80.499 174.734,79.141L174.734,79.141C174.793,78.859 174.763,78.593 174.573,78.375L174.573,78.375C174.398,78.175 173.919,78.094 173.358,78.094L173.358,78.094C172.417,78.094 171.247,78.323 170.913,78.602L170.913,78.602Z M 0,0" />
        <path
            android:pathData="M169.208,90.8l6.53,0l0,-13.63l-6.53,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M169.595,88.209C169.983,88.566 170.425,88.618 170.86,88.834L170.86,88.834C171.288,89.049 171.514,89.501 171.831,89.84L171.831,89.84C172.181,90.217 172.627,90.458 173.111,90.6L173.111,90.6C173.252,90.641 174.857,90.936 174.836,90.863L174.836,90.863C174.72,90.457 174.392,90.065 174.102,89.712L174.102,89.712C173.921,89.49 173.643,89.068 173.373,88.964L173.373,88.964C173.424,89.191 173.681,89.467 173.613,89.692L173.613,89.692C173.123,89.597 172.668,89.265 172.277,88.963L172.277,88.963C171.93,88.696 171.634,88.376 171.331,88.055L171.331,88.055C171.182,87.898 169.486,86.156 169.652,86.009L169.652,86.009C169.062,86.492 169.024,87.681 169.595,88.209 M 0,0" />
        <path
            android:pathData="M169.596,88.209C169.983,88.566 170.425,88.618 170.86,88.834L170.86,88.834C171.288,89.049 171.514,89.501 171.831,89.84L171.831,89.84C172.181,90.217 172.627,90.458 173.111,90.6L173.111,90.6C173.252,90.641 174.857,90.936 174.836,90.863L174.836,90.863C174.72,90.457 174.392,90.065 174.102,89.712L174.102,89.712C173.921,89.49 173.643,89.068 173.373,88.964L173.373,88.964C173.423,89.191 173.681,89.467 173.613,89.692L173.613,89.692C173.124,89.597 172.667,89.265 172.277,88.963L172.277,88.963C171.93,88.696 171.634,88.376 171.331,88.055L171.331,88.055C171.182,87.898 169.486,86.156 169.652,86.009L169.652,86.009C169.061,86.492 169.024,87.681 169.596,88.209"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="88.47247"
                    android:startX="169.02397"
                    android:endY="88.47247"
                    android:endX="174.85698"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFD958E" />
                    <item
                        android:offset="1"
                        android:color="#FFF67C77" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M169.595,88.209C169.983,88.566 170.425,88.618 170.86,88.834L170.86,88.834C171.288,89.049 171.514,89.501 171.831,89.84L171.831,89.84C172.181,90.217 172.627,90.458 173.111,90.6L173.111,90.6C173.252,90.641 174.857,90.936 174.836,90.863L174.836,90.863C174.72,90.457 174.392,90.065 174.102,89.712L174.102,89.712C173.921,89.49 173.643,89.068 173.373,88.964L173.373,88.964C173.424,89.191 173.681,89.467 173.613,89.692L173.613,89.692C173.123,89.597 172.668,89.265 172.277,88.963L172.277,88.963C171.93,88.696 171.634,88.376 171.331,88.055L171.331,88.055C171.182,87.898 169.486,86.156 169.652,86.009L169.652,86.009C169.062,86.492 169.024,87.681 169.595,88.209 M 0,0" />
        <path
            android:pathData="M169.596,88.209C169.983,88.566 170.425,88.618 170.86,88.834L170.86,88.834C171.288,89.049 171.514,89.501 171.831,89.84L171.831,89.84C172.181,90.217 172.627,90.458 173.111,90.6L173.111,90.6C173.252,90.641 174.857,90.936 174.836,90.863L174.836,90.863C174.72,90.457 174.392,90.065 174.102,89.712L174.102,89.712C173.921,89.49 173.643,89.068 173.373,88.964L173.373,88.964C173.423,89.191 173.681,89.467 173.613,89.692L173.613,89.692C173.124,89.597 172.667,89.265 172.277,88.963L172.277,88.963C171.93,88.696 171.634,88.376 171.331,88.055L171.331,88.055C171.182,87.898 169.486,86.156 169.652,86.009L169.652,86.009C169.061,86.492 169.024,87.681 169.596,88.209"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="99.793434"
                    android:startX="5.760641"
                    android:endY="99.793434"
                    android:endX="237.80975"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFD958E" />
                    <item
                        android:offset="1"
                        android:color="#FFF67C77" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M169.595,88.209C169.983,88.566 170.425,88.618 170.86,88.834L170.86,88.834C171.288,89.049 171.514,89.501 171.831,89.84L171.831,89.84C172.181,90.217 172.627,90.458 173.111,90.6L173.111,90.6C173.252,90.641 174.857,90.936 174.836,90.863L174.836,90.863C174.72,90.457 174.392,90.065 174.102,89.712L174.102,89.712C173.921,89.49 173.643,89.068 173.373,88.964L173.373,88.964C173.424,89.191 173.681,89.467 173.613,89.692L173.613,89.692C173.123,89.597 172.668,89.265 172.277,88.963L172.277,88.963C171.93,88.696 171.634,88.376 171.331,88.055L171.331,88.055C171.182,87.898 169.486,86.156 169.652,86.009L169.652,86.009C169.062,86.492 169.024,87.681 169.595,88.209 M 0,0" />
        <path
            android:pathData="M169.596,88.209C169.983,88.566 170.425,88.618 170.86,88.834L170.86,88.834C171.288,89.049 171.514,89.501 171.831,89.84L171.831,89.84C172.181,90.217 172.627,90.458 173.111,90.6L173.111,90.6C173.252,90.641 174.857,90.936 174.836,90.863L174.836,90.863C174.72,90.457 174.392,90.065 174.102,89.712L174.102,89.712C173.921,89.49 173.643,89.068 173.373,88.964L173.373,88.964C173.423,89.191 173.681,89.467 173.613,89.692L173.613,89.692C173.124,89.597 172.667,89.265 172.277,88.963L172.277,88.963C171.93,88.696 171.634,88.376 171.331,88.055L171.331,88.055C171.182,87.898 169.486,86.156 169.652,86.009L169.652,86.009C169.061,86.492 169.024,87.681 169.596,88.209"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="88.47253"
                    android:startX="168.03987"
                    android:endY="88.47253"
                    android:endX="175.8411"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFD958E" />
                    <item
                        android:offset="1"
                        android:color="#FFF67C77" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M156.692,22.724L156.694,22.725L156.692,22.724Z M 0,0" />
        <path
            android:pathData="M155.421,23.994l2.544,0l0,-2.542l-2.544,0z"
            android:strokeWidth="1"
            android:fillColor="#8FC4A1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M153.271,21.583L153.271,21.579L153.271,21.583Z M 0,0" />
        <path
            android:pathData="M151.999,22.854l2.543,0l0,-2.546l-2.543,0z"
            android:strokeWidth="1"
            android:fillColor="#508CAE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M157.424,34.268C157.036,34.273 157.044,34.884 157.431,34.879L157.431,34.879C157.818,34.874 157.812,34.268 157.429,34.268L157.429,34.268C157.427,34.268 157.426,34.268 157.424,34.268L157.424,34.268Z M 0,0" />
        <path
            android:pathData="M156.29,35.726l2.276,0l0,-2.305l-2.276,0z"
            android:strokeWidth="1"
            android:fillColor="#FFFFFE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M153.502,29.116C153.114,29.12 153.121,29.731 153.51,29.727L153.51,29.727C153.896,29.722 153.89,29.116 153.506,29.116L153.506,29.116C153.505,29.116 153.504,29.116 153.502,29.116L153.502,29.116Z M 0,0" />
        <path
            android:pathData="M152.368,30.574l2.276,0l0,-2.305l-2.276,0z"
            android:strokeWidth="1"
            android:fillColor="#FFFFFE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M149.454,41.701C149.491,41.628 149.505,41.555 149.506,41.481L149.506,41.481C149.488,41.555 149.47,41.628 149.454,41.701L149.454,41.701Z M 0,0" />
        <path
            android:pathData="M148.184,42.972l2.593,0l0,-2.762l-2.593,0z"
            android:strokeWidth="1"
            android:fillColor="#D9923D"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M151.384,22.464L151.427,22.147C151.401,22.25 151.388,22.356 151.384,22.464L151.384,22.464Z M 0,0" />
        <path
            android:pathData="M150.113,23.735l2.584,0l0,-2.859l-2.584,0z"
            android:strokeWidth="1"
            android:fillColor="#8EA9A6"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M151.315,22.974C151.347,22.898 151.373,22.822 151.395,22.744L151.395,22.744C151.384,22.65 151.38,22.556 151.384,22.464L151.384,22.464L151.315,22.974Z M 0,0" />
        <path
            android:pathData="M150.044,24.244l2.622,0l0,-3.051l-2.622,0z"
            android:strokeWidth="1"
            android:fillColor="#8EA9A6"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M181.354,62.34C179.079,62.374 177.994,63.649 177.12,64.955L177.12,64.955C174.287,69.196 173.018,82.221 173.018,82.221L173.018,82.221C173.018,82.221 172.723,83.667 175.358,84.522L175.358,84.522C177.884,85.341 178.823,84.508 178.823,84.508L178.823,84.508C179.745,81.346 179.626,81.79 179.718,81.504L179.718,81.504C179.968,80.736 180.289,79.995 180.533,79.225L180.533,79.225C181.375,76.571 182.59,74.055 183.781,71.538L183.781,71.538C184.714,69.564 186.188,67.877 185.763,65.483L185.763,65.483C185.474,63.862 183.518,62.339 181.42,62.339L181.42,62.339C181.398,62.339 181.376,62.339 181.354,62.34L181.354,62.34Z M 0,0" />
        <path
            android:pathData="M172.064,85.856l14.718,0l0,-24.491l-14.718,0z"
            android:strokeWidth="1"
            android:fillColor="#121322"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M153.224,47.755C152.565,48.319 151.8,48.106 151.344,47.607L151.344,47.607C150.801,48.42 150.305,49.223 150.267,50.37L150.267,50.37C150.2,52.317 151.24,54.166 152.445,55.824L152.445,55.824C154.919,59.229 158.652,62.439 162.738,62.949L162.738,62.949C175.398,64.529 180.591,75.449 185.377,68.411L185.377,68.411C186.932,66.127 184.439,62.403 181.919,60.96L181.919,60.96C178.901,59.232 163.559,46.931 157.881,43.151L157.881,43.151C156.888,44.798 154.724,46.471 153.224,47.755L153.224,47.755Z M 0,0" />
        <path
            android:pathData="M149.31,71.727l37.501,0l0,-29.552l-37.501,0z"
            android:strokeWidth="1"
            android:fillColor="#121322"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M176.895,51.596C176.494,51.79 176.178,52.629 175.987,52.977L175.987,52.977C175.218,54.38 174.665,55.908 173.899,57.314L173.899,57.314C171.921,60.948 170.813,65.125 169.775,69.125L169.775,69.125C169.251,71.138 169.345,75.315 169.334,77.61L169.334,77.61C169.322,79.955 173.85,80.288 174.531,79.145L174.531,79.145C175.283,77.881 177.42,70.138 178.511,67.53L178.511,67.53C179.649,64.809 181.201,62.233 181.971,59.371L181.971,59.371C182.463,57.539 183.31,54.507 182.193,52.749L182.193,52.749C181.528,51.702 180.135,51.194 178.802,51.194L178.802,51.194C178.114,51.194 177.442,51.329 176.895,51.596L176.895,51.596Z M 0,0" />
        <path
            android:pathData="M168.345,80.751l15.387,0l0,-30.526l-15.387,0z"
            android:strokeWidth="1"
            android:fillColor="#121322"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M148.975,46.325C148.284,46.551 147.973,45.798 147.229,45.775L147.229,45.775C146.726,47.901 145.131,51.209 145.362,53.398L145.362,53.398C146.262,61.902 157.726,62.314 163.561,61.824L163.561,61.824C168.616,61.399 173.603,59.689 178.696,60.014L178.696,60.014C179.828,60.086 179.547,59.81 179.329,59.206L179.329,59.206C179.197,58.839 179.088,58.35 179.332,57.745L179.332,57.745C179.935,56.251 180.623,54.979 180.889,53.366L180.889,53.366C179.944,51.278 178.274,51.922 176.423,51.372L176.423,51.372C174.637,50.842 171.925,50.974 169.136,51.107L169.136,51.107C164.335,51.335 159.304,51.563 158.364,48.412L158.364,48.412C157.806,46.543 156.577,45.341 153.997,45.34L153.997,45.34C152.695,45.34 151.051,45.646 148.975,46.325L148.975,46.325Z M 0,0" />
        <path
            android:pathData="M144.386,62.942l37.456,0l0,-18.584l-37.456,0z"
            android:strokeWidth="1"
            android:fillColor="#121322"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M159.052,43.176C158.798,43.179 158.483,43.43 158.179,43.536L158.179,43.536C157.559,43.749 156.929,43.971 156.29,44.108L156.29,44.108C153.811,44.643 151.322,44.813 148.812,44.812L148.812,44.812C148.163,44.812 147.498,44.568 146.864,44.455L146.864,44.455C146.288,44.35 146.151,44.45 146.132,45.052L146.132,45.052C146.12,45.459 146.004,46.08 146.238,46.43L146.238,46.43C146.452,46.751 146.911,46.754 147.258,46.808L147.258,46.808C147.972,46.92 148.699,46.924 149.422,46.966L149.422,46.966C150.643,47.037 151.872,46.969 153.093,46.813L153.093,46.813C154.239,46.666 155.384,46.555 156.518,46.293L156.518,46.293C157.864,45.983 160.492,45.271 159.356,43.365L159.356,43.365C159.272,43.227 159.171,43.176 159.057,43.176L159.057,43.176C159.055,43.176 159.053,43.176 159.052,43.176L159.052,43.176Z M 0,0" />
        <path
            android:pathData="M145.104,47.919l15.518,0l0,-5.667l-15.518,0z"
            android:strokeWidth="1"
            android:fillColor="#121322"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M172.034,80.83C174.148,81.04 174.828,80.919 174.888,78.726L174.888,78.726C173.914,79.539 170.306,79.41 169.285,78.325L169.285,78.325C168.608,80.411 169.91,80.618 172.034,80.83L172.034,80.83Z M 0,0" />
        <path
            android:pathData="M168.152,81.749l7.69,0l0,-4.271l-7.69,0z"
            android:strokeWidth="1"
            android:fillColor="#121322"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M149.414,44.72C149.451,44.646 149.465,44.57 149.466,44.494L149.466,44.494C149.448,44.571 149.431,44.646 149.414,44.72L149.414,44.72Z M 0,0" />
        <path
            android:pathData="M148.143,45.991l2.594,0l0,-2.767l-2.594,0z"
            android:strokeWidth="1"
            android:fillColor="#D9923D"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M148.411,26.94C148.943,27.03 149.082,26.203 148.549,26.115L148.549,26.115C148.521,26.11 148.495,26.108 148.47,26.108L148.47,26.108C148.004,26.108 147.906,26.855 148.411,26.94L148.411,26.94Z M 0,0" />
        <path
            android:pathData="M146.803,28.218l3.353,0l0,-3.38l-3.353,0z"
            android:strokeWidth="1"
            android:fillColor="#FFFFFE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M149.416,29.874C149.771,29.933 149.863,29.383 149.508,29.324L149.508,29.324C149.49,29.321 149.473,29.319 149.456,29.319L149.456,29.319C149.145,29.319 149.079,29.818 149.416,29.874L149.416,29.874Z M 0,0" />
        <path
            android:pathData="M148.345,30.726l2.235,0l0,-2.254l-2.235,0z"
            android:strokeWidth="1"
            android:fillColor="#FFFFFE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M153.075,42.449C153.05,42.599 153.154,42.745 153.304,42.77L153.304,42.77L153.414,42.789C153.564,42.814 153.71,42.709 153.735,42.559L153.735,42.559C153.76,42.409 153.656,42.263 153.506,42.238L153.506,42.238L153.396,42.22C153.381,42.217 153.366,42.216 153.351,42.216L153.351,42.216C153.218,42.216 153.097,42.314 153.075,42.449L153.075,42.449Z M 0,0" />
        <path
            android:pathData="M152.224,43.64l2.362,0l0,-2.271l-2.362,0z"
            android:strokeWidth="1"
            android:fillColor="#FFFFFE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M157.266,36.393C157.241,36.618 157.345,36.836 157.496,36.874L157.496,36.874L157.606,36.902C157.756,36.94 157.902,36.783 157.927,36.558L157.927,36.558C157.952,36.333 157.847,36.114 157.697,36.076L157.697,36.076L157.588,36.049C157.573,36.045 157.557,36.043 157.543,36.043L157.543,36.043C157.409,36.043 157.289,36.19 157.266,36.393L157.266,36.393Z M 0,0" />
        <path
            android:pathData="M156.415,38.179l2.362,0l0,-3.406l-2.362,0z"
            android:strokeWidth="1"
            android:fillColor="#FFFFFE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M159.554,34.716C159.909,34.775 160.001,34.224 159.646,34.165L159.646,34.165C159.628,34.162 159.611,34.161 159.594,34.161L159.594,34.161C159.283,34.161 159.218,34.659 159.554,34.716L159.554,34.716Z M 0,0" />
        <path
            android:pathData="M158.483,35.567l2.235,0l0,-2.254l-2.235,0z"
            android:strokeWidth="1"
            android:fillColor="#FFFFFE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M150.165,43.488C150.14,43.638 150.244,43.784 150.394,43.81L150.394,43.81L150.504,43.828C150.654,43.853 150.8,43.749 150.825,43.599L150.825,43.599C150.85,43.449 150.746,43.303 150.596,43.277L150.596,43.277L150.486,43.259C150.471,43.257 150.456,43.255 150.441,43.255L150.441,43.255C150.308,43.255 150.187,43.353 150.165,43.488L150.165,43.488Z M 0,0" />
        <path
            android:pathData="M149.314,44.679l2.362,0l0,-2.271l-2.362,0z"
            android:strokeWidth="1"
            android:fillColor="#FFFFFE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M144.802,25.576C143.305,27.937 144.844,30.739 145.239,33.492L145.239,33.492C145.828,37.605 145.597,38.635 146.003,43.725L146.003,43.725C146.17,45.817 156.504,44.646 158.467,43.369L158.467,43.369C159.799,42.502 159.078,39.762 159.15,37.066L159.15,37.066C159.209,34.821 160.391,32.813 160.719,30.614L160.719,30.614C160.91,29.33 160.598,27.979 160.363,26.717L160.363,26.717C160.164,25.649 160.219,24.197 159.819,23.221L159.819,23.221C159.679,22.88 159.435,22.632 159.142,22.438L159.142,22.438C158.646,22.112 158.011,21.947 157.488,21.768L157.488,21.768C157.388,21.734 157.294,21.687 157.208,21.632L157.208,21.632C157.604,21.887 155.033,23.582 154.793,23.658L154.793,23.658C152.8,24.283 151.67,23.126 151.132,21.309L151.132,21.309C149.126,22.692 146.648,22.668 144.802,25.576 M 0,0" />
        <path
            android:pathData="M144.802,25.576C143.305,27.937 144.844,30.739 145.239,33.492L145.239,33.492C145.828,37.605 145.597,38.635 146.003,43.725L146.003,43.725C146.17,45.817 156.504,44.646 158.467,43.369L158.467,43.369C159.799,42.502 159.078,39.762 159.15,37.066L159.15,37.066C159.209,34.821 160.391,32.813 160.719,30.614L160.719,30.614C160.91,29.33 160.598,27.979 160.363,26.717L160.363,26.717C160.164,25.649 160.219,24.197 159.819,23.221L159.819,23.221C159.679,22.88 159.435,22.632 159.142,22.438L159.142,22.438C158.646,22.112 158.011,21.947 157.488,21.768L157.488,21.768C157.388,21.734 157.294,21.687 157.208,21.632L157.208,21.632C157.604,21.887 155.033,23.582 154.793,23.658L154.793,23.658C152.8,24.283 151.67,23.126 151.132,21.309L151.132,21.309C149.126,22.692 146.648,22.668 144.802,25.576"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="33.5629"
                    android:startX="143.30498"
                    android:endY="33.5629"
                    android:endX="160.91002"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFD958E" />
                    <item
                        android:offset="1"
                        android:color="#FFF67C77" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M144.802,25.576C143.305,27.937 144.844,30.739 145.239,33.492L145.239,33.492C145.828,37.605 145.597,38.635 146.003,43.725L146.003,43.725C146.17,45.817 156.504,44.646 158.467,43.369L158.467,43.369C159.799,42.502 159.078,39.762 159.15,37.066L159.15,37.066C159.209,34.821 160.391,32.813 160.719,30.614L160.719,30.614C160.91,29.33 160.598,27.979 160.363,26.717L160.363,26.717C160.164,25.649 160.219,24.197 159.819,23.221L159.819,23.221C159.679,22.88 159.435,22.632 159.142,22.438L159.142,22.438C158.646,22.112 158.011,21.947 157.488,21.768L157.488,21.768C157.388,21.734 157.294,21.687 157.208,21.632L157.208,21.632C157.604,21.887 155.033,23.582 154.793,23.658L154.793,23.658C152.8,24.283 151.67,23.126 151.132,21.309L151.132,21.309C149.126,22.692 146.648,22.668 144.802,25.576 M 0,0" />
        <path
            android:pathData="M144.802,25.576C143.305,27.937 144.844,30.739 145.239,33.492L145.239,33.492C145.828,37.605 145.597,38.635 146.003,43.725L146.003,43.725C146.17,45.817 156.504,44.646 158.467,43.369L158.467,43.369C159.799,42.502 159.078,39.762 159.15,37.066L159.15,37.066C159.209,34.821 160.391,32.813 160.719,30.614L160.719,30.614C160.91,29.33 160.598,27.979 160.363,26.717L160.363,26.717C160.164,25.649 160.219,24.197 159.819,23.221L159.819,23.221C159.679,22.88 159.435,22.632 159.142,22.438L159.142,22.438C158.646,22.112 158.011,21.947 157.488,21.768L157.488,21.768C157.388,21.734 157.294,21.687 157.208,21.632L157.208,21.632C157.604,21.887 155.033,23.582 154.793,23.658L154.793,23.658C152.8,24.283 151.67,23.126 151.132,21.309L151.132,21.309C149.126,22.692 146.648,22.668 144.802,25.576"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="103.57269"
                    android:startX="1.0067905"
                    android:endY="103.57269"
                    android:endX="240.84421"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFD958E" />
                    <item
                        android:offset="1"
                        android:color="#FFF67C77" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M144.802,25.576C143.305,27.937 144.844,30.739 145.239,33.492L145.239,33.492C145.828,37.605 145.597,38.635 146.003,43.725L146.003,43.725C146.17,45.817 156.504,44.646 158.467,43.369L158.467,43.369C159.799,42.502 159.078,39.762 159.15,37.066L159.15,37.066C159.209,34.821 160.391,32.813 160.719,30.614L160.719,30.614C160.91,29.33 160.598,27.979 160.363,26.717L160.363,26.717C160.164,25.649 160.219,24.197 159.819,23.221L159.819,23.221C159.679,22.88 159.435,22.632 159.142,22.438L159.142,22.438C158.646,22.112 158.011,21.947 157.488,21.768L157.488,21.768C157.388,21.734 157.294,21.687 157.208,21.632L157.208,21.632C157.604,21.887 155.033,23.582 154.793,23.658L154.793,23.658C152.8,24.283 151.67,23.126 151.132,21.309L151.132,21.309C149.126,22.692 146.648,22.668 144.802,25.576 M 0,0" />
        <path
            android:pathData="M144.802,25.576C143.305,27.937 144.844,30.739 145.239,33.492L145.239,33.492C145.828,37.605 145.597,38.635 146.003,43.725L146.003,43.725C146.17,45.817 156.504,44.646 158.467,43.369L158.467,43.369C159.799,42.502 159.078,39.762 159.15,37.066L159.15,37.066C159.209,34.821 160.391,32.813 160.719,30.614L160.719,30.614C160.91,29.33 160.598,27.979 160.363,26.717L160.363,26.717C160.164,25.649 160.219,24.197 159.819,23.221L159.819,23.221C159.679,22.88 159.435,22.632 159.142,22.438L159.142,22.438C158.646,22.112 158.011,21.947 157.488,21.768L157.488,21.768C157.388,21.734 157.294,21.687 157.208,21.632L157.208,21.632C157.604,21.887 155.033,23.582 154.793,23.658L154.793,23.658C152.8,24.283 151.67,23.126 151.132,21.309L151.132,21.309C149.126,22.692 146.648,22.668 144.802,25.576"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="33.562866"
                    android:startX="142.2879"
                    android:endY="33.562866"
                    android:endX="161.9271"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFD958E" />
                    <item
                        android:offset="1"
                        android:color="#FFF67C77" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M148.773,31.515C148.379,31.789 147.802,33.42 147.423,33.921L147.423,33.921C147.17,34.257 144.852,37.662 144.361,37.042L144.361,37.042C144.924,37.753 145.606,38.442 146.195,39.085L146.195,39.085C147.107,40.083 147.989,41.072 148.969,42.005L148.969,42.005C150.717,43.67 154.189,45.774 156.462,44.172L156.462,44.172C159.008,42.376 156.979,40.417 155.492,38.813L155.492,38.813C153.336,36.487 152.413,34.437 150.62,32.615L150.62,32.615C150.171,32.159 149.721,31.376 149.17,31.376L149.17,31.376C149.044,31.376 148.912,31.418 148.773,31.515L148.773,31.515Z M 0,0" />
        <path
            android:pathData="M143.399,45.731l15.237,0l0,-15.343l-15.237,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M152.253,41.621C150.987,46.391 159.048,47.041 161.807,46.686L161.807,46.686C164.155,46.383 166.511,45.9 168.731,45.137L168.731,45.137C168.936,45.067 168.803,41.75 168.601,41.775L168.601,41.775C165.853,42.106 163.268,41.569 162.415,41.477L162.415,41.477C160.016,41.219 159.009,41.17 156.967,40.867L156.967,40.867C156.088,40.736 154.955,40.346 154.007,40.345L154.007,40.345C153.193,40.345 152.515,40.633 152.253,41.621L152.253,41.621Z M 0,0" />
        <path
            android:pathData="M151.138,47.704l18.678,0l0,-8.295l-18.678,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M170.595,39.38C170.163,39.733 169.897,40.207 169.421,40.507L169.421,40.507C168.952,40.8 168.305,41.057 168.114,41.768L168.114,41.768C168.102,41.811 168.092,41.86 168.084,41.913L168.084,41.913C168.009,42.431 168.109,43.561 168.613,43.632L168.613,43.632C169.18,43.713 169.805,42.38 170.075,41.873L170.075,41.873C170.287,41.473 170.555,41.019 170.697,40.574L170.697,40.574C170.741,40.437 170.842,39.355 170.651,39.355L170.651,39.355C170.634,39.355 170.616,39.363 170.595,39.38L170.595,39.38Z M 0,0" />
        <path
            android:pathData="M167.215,44.613l4.396,0l0,-6.236l-4.396,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M169.756,41.193C169.51,41.276 169.173,41.719 169.01,41.916L169.01,41.916C168.604,42.413 168.354,43.036 168.07,43.61L168.07,43.61C167.808,44.135 167.544,44.922 167.08,45.302L167.08,45.302L167.105,45.362C168.19,46.222 171.293,46.01 172.044,45.71L172.044,45.71C172.58,45.494 172.832,45.481 173.349,45.205L173.349,45.205C173.743,44.995 174.727,43.834 173.717,44.186L173.717,44.186C174.14,44.038 174.897,43.251 174.801,42.674L174.801,42.674C174.728,42.59 174.622,42.631 174.513,42.672L174.513,42.672C174.439,42.7 174.364,42.728 174.296,42.717L174.296,42.717C174.354,42.645 175.146,41.959 174.852,41.738L174.852,41.738C174.407,41.411 174.184,41.873 173.716,41.943L173.716,41.943C173.972,41.905 173.85,41.471 173.748,41.37L173.748,41.37C173.548,41.172 173.34,41.273 173.119,41.338L173.119,41.338C172.408,41.55 171.703,41.54 170.976,41.395L170.976,41.395C170.724,41.345 170.251,41.17 169.929,41.17L169.929,41.17C169.864,41.17 169.806,41.177 169.756,41.193L169.756,41.193Z M 0,0" />
        <path
            android:pathData="M166.064,47.031l9.87,0l0,-6.92l-9.87,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M142.21,29.072C143.042,31.986 143.123,34.191 143.333,35.95L143.333,35.95C143.628,38.435 144.583,37.912 146.584,37.088L146.584,37.088C148.657,36.235 150.97,36.082 152.927,35.24L152.927,35.24C152.519,33.91 151.589,32.754 151.275,31.369L151.275,31.369C150.967,30.011 151.39,28.488 151.095,27.088L151.095,27.088C150.843,25.89 150.691,24.487 149.718,23.827L149.718,23.827C148.919,23.284 148.111,23.053 147.335,23.053L147.335,23.053C144.189,23.053 141.576,26.853 142.21,29.072 M 0,0" />
        <path
            android:pathData="M142.21,29.072C143.042,31.986 143.123,34.191 143.333,35.95L143.333,35.95C143.628,38.435 144.583,37.912 146.584,37.088L146.584,37.088C148.657,36.235 150.97,36.082 152.927,35.24L152.927,35.24C152.519,33.91 151.589,32.754 151.275,31.369L151.275,31.369C150.967,30.011 151.39,28.488 151.095,27.088L151.095,27.088C150.843,25.89 150.691,24.487 149.718,23.827L149.718,23.827C148.919,23.284 148.111,23.053 147.335,23.053L147.335,23.053C144.189,23.053 141.576,26.853 142.21,29.072"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="30.744194"
                    android:startX="141.5759"
                    android:endY="30.744194"
                    android:endX="152.92723"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFC1C1" />
                    <item
                        android:offset="1"
                        android:color="#FFFD958E" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M142.21,29.072C143.042,31.986 143.123,34.191 143.333,35.95L143.333,35.95C143.628,38.435 144.583,37.912 146.584,37.088L146.584,37.088C148.657,36.235 150.97,36.082 152.927,35.24L152.927,35.24C152.519,33.91 151.589,32.754 151.275,31.369L151.275,31.369C150.967,30.011 151.39,28.488 151.095,27.088L151.095,27.088C150.843,25.89 150.691,24.487 149.718,23.827L149.718,23.827C148.919,23.284 148.111,23.053 147.335,23.053L147.335,23.053C144.189,23.053 141.576,26.853 142.21,29.072 M 0,0" />
        <path
            android:pathData="M142.21,29.072C143.042,31.986 143.123,34.191 143.333,35.95L143.333,35.95C143.628,38.435 144.583,37.912 146.584,37.088L146.584,37.088C148.657,36.235 150.97,36.082 152.927,35.24L152.927,35.24C152.519,33.91 151.589,32.754 151.275,31.369L151.275,31.369C150.967,30.011 151.39,28.488 151.095,27.088L151.095,27.088C150.843,25.89 150.691,24.487 149.718,23.827L149.718,23.827C148.919,23.284 148.111,23.053 147.335,23.053L147.335,23.053C144.189,23.053 141.576,26.853 142.21,29.072"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="104.76696"
                    android:startX="2.4356282"
                    android:endY="104.76696"
                    android:endX="241.0598"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFC1C1" />
                    <item
                        android:offset="1"
                        android:color="#FFFD958E" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M142.21,29.072C143.042,31.986 143.123,34.191 143.333,35.95L143.333,35.95C143.628,38.435 144.583,37.912 146.584,37.088L146.584,37.088C148.657,36.235 150.97,36.082 152.927,35.24L152.927,35.24C152.519,33.91 151.589,32.754 151.275,31.369L151.275,31.369C150.967,30.011 151.39,28.488 151.095,27.088L151.095,27.088C150.843,25.89 150.691,24.487 149.718,23.827L149.718,23.827C148.919,23.284 148.111,23.053 147.335,23.053L147.335,23.053C144.189,23.053 141.576,26.853 142.21,29.072 M 0,0" />
        <path
            android:pathData="M142.21,29.072C143.042,31.986 143.123,34.191 143.333,35.95L143.333,35.95C143.628,38.435 144.583,37.912 146.584,37.088L146.584,37.088C148.657,36.235 150.97,36.082 152.927,35.24L152.927,35.24C152.519,33.91 151.589,32.754 151.275,31.369L151.275,31.369C150.967,30.011 151.39,28.488 151.095,27.088L151.095,27.088C150.843,25.89 150.691,24.487 149.718,23.827L149.718,23.827C148.919,23.284 148.111,23.053 147.335,23.053L147.335,23.053C144.189,23.053 141.576,26.853 142.21,29.072"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="30.74398"
                    android:startX="140.56387"
                    android:endY="30.74398"
                    android:endX="153.93924"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFC1C1" />
                    <item
                        android:offset="1"
                        android:color="#FFFD958E" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M150.981,43.68C153.168,45.883 158.997,46.669 158.997,46.669L158.997,46.669L150.966,39.088C150.966,39.088 148.794,41.476 150.981,43.68L150.981,43.68Z M 0,0" />
        <path
            android:pathData="M149.01,47.637l10.982,0l0,-9.517l-10.982,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M155,15.831C155,15.831 156.825,24.252 158.514,22.54L158.514,22.54C158.514,22.54 161.211,21.121 160.227,18.765L160.227,18.765C160.227,18.765 159.265,17.08 159.539,16.021L159.539,16.021C159.812,14.961 162.402,11.798 161.89,10.202L161.89,10.202C161.378,8.607 162.088,6.214 160.39,3.442L160.39,3.442C160.247,3.21 160.091,3.103 159.924,3.103L159.924,3.103C158.099,3.102 155,15.831 155,15.831L155,15.831Z M 0,0" />
        <path
            android:pathData="M154.011,23.747l8.934,0l0,-21.622l-8.934,0z"
            android:strokeWidth="1"
            android:fillColor="#151324"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M149.367,7.497C149.327,10.968 152.109,13.816 155.58,13.856L155.58,13.856C159.052,13.896 161.899,11.114 161.939,7.643L161.939,7.643C161.979,4.171 159.198,1.325 155.726,1.284L155.726,1.284C155.701,1.284 155.677,1.284 155.652,1.284L155.652,1.284C152.214,1.284 149.408,4.05 149.367,7.497L149.367,7.497Z M 0,0" />
        <path
            android:pathData="M148.395,14.828l14.516,0l0,-14.516l-14.516,0z"
            android:strokeWidth="1"
            android:fillColor="#101523"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M154.947,7.205C153.419,7.205 152.153,9.673 152.117,12.732L152.117,12.732L152.03,20.056C151.994,23.125 153.194,24.658 154.729,24.676L154.729,24.676C156.265,24.693 157.555,23.189 157.591,20.119L157.591,20.119L157.678,12.796C157.714,9.726 156.498,7.222 154.963,7.205L154.963,7.205C154.958,7.205 154.952,7.205 154.947,7.205L154.947,7.205Z M 0,0" />
        <path
            android:pathData="M151.077,25.609l7.555,0l0,-19.338l-7.555,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M154.593,15.375C156.106,15.983 157.633,18.602 157.633,18.602L157.633,18.602L157.679,14.014C156.988,15.667 155.358,15.539 154.593,15.375L154.593,15.375ZM154.593,15.375C154.455,15.32 154.318,15.281 154.182,15.262L154.182,15.262C154.182,15.262 154.34,15.321 154.593,15.375L154.593,15.375Z M 0,0" />
        <path
            android:pathData="M153.257,19.66l5.346,0l0,-6.705l-5.346,0z"
            android:strokeWidth="1"
            android:fillColor="#B08477"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M151.242,6.183L151.176,11.532C151.148,13.774 153.057,15.614 155.439,15.64L155.439,15.64C157.821,15.665 159.774,13.869 159.801,11.626L159.801,11.626L159.867,6.277C159.895,4.034 157.986,2.195 155.605,2.169L155.605,2.169C155.588,2.169 155.571,2.169 155.554,2.169L155.554,2.169C153.195,2.169 151.269,3.956 151.242,6.183L151.242,6.183Z M 0,0" />
        <path
            android:pathData="M150.181,16.576l10.681,0l0,-15.343l-10.681,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M156.055,7.289C155.111,7.289 154.336,8.052 154.326,8.999L154.326,8.999C154.314,9.953 155.082,10.738 156.036,10.749L156.036,10.749C156.99,10.76 157.775,9.993 157.786,9.039L157.786,9.039C157.787,8.969 157.784,8.899 157.776,8.829L157.776,8.829C157.681,7.961 156.95,7.298 156.076,7.289L156.076,7.289C156.069,7.289 156.062,7.289 156.055,7.289L156.055,7.289ZM154.48,9C154.49,8.132 155.205,7.432 156.074,7.442L156.074,7.442C156.87,7.452 157.536,8.055 157.622,8.846L157.622,8.846C157.63,8.909 157.633,8.974 157.632,9.037L157.632,9.037C157.622,9.9 156.917,10.595 156.056,10.595L156.056,10.595C156.05,10.595 156.044,10.595 156.038,10.595L156.038,10.595C155.168,10.585 154.47,9.87 154.48,9L154.48,9Z M 0,0" />
        <path
            android:pathData="M153.309,11.766l5.494,0l0,-5.494l-5.494,0z"
            android:strokeWidth="1"
            android:fillColor="#0A0B09"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M159.98,7.338C159.035,7.338 158.26,8.102 158.249,9.049L158.249,9.049C158.238,10.003 159.006,10.788 159.96,10.799L159.96,10.799C160.914,10.81 161.699,10.043 161.71,9.089L161.71,9.089C161.711,9.019 161.707,8.949 161.7,8.879L161.7,8.879C161.605,8.011 160.874,7.348 160,7.338L160,7.338C159.993,7.338 159.987,7.338 159.98,7.338L159.98,7.338ZM158.403,9.05C158.414,8.182 159.129,7.482 159.998,7.492L159.998,7.492C160.794,7.502 161.46,8.105 161.546,8.896L161.546,8.896C161.553,8.959 161.557,9.024 161.556,9.087L161.556,9.087C161.546,9.95 160.84,10.645 159.98,10.645L159.98,10.645C159.974,10.645 159.968,10.645 159.962,10.645L159.962,10.645C159.092,10.635 158.393,9.92 158.403,9.05L158.403,9.05Z M 0,0" />
        <path
            android:pathData="M157.233,11.816l5.494,0l0,-5.494l-5.494,0z"
            android:strokeWidth="1"
            android:fillColor="#0A0B09"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M151.718,4.083C150.377,6.687 152.015,9.717 152.015,9.717L152.015,9.717C152.015,9.717 154.953,7.446 156.5,4.441L156.5,4.441C156.5,4.441 157.015,3.489 156.628,2.074L156.628,2.074C156.628,2.074 156.276,2.015 155.752,2.015L155.752,2.015C154.607,2.015 152.638,2.296 151.718,4.083L151.718,4.083Z M 0,0" />
        <path
            android:pathData="M150.174,10.734l7.615,0l0,-9.735l-7.615,0z"
            android:strokeWidth="1"
            android:fillColor="#101523"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M160.416,7.34C160.381,7.34 160.352,7.374 160.352,7.416L160.352,7.416C160.352,7.459 160.38,7.493 160.415,7.494L160.415,7.494C161.079,7.503 161.634,8.106 161.706,8.897L161.706,8.897C161.712,8.96 161.714,9.024 161.714,9.088L161.714,9.088C161.705,9.957 161.109,10.656 160.385,10.646L160.385,10.646C160.349,10.646 160.32,10.68 160.32,10.722L160.32,10.722C160.32,10.765 160.348,10.8 160.384,10.8L160.384,10.8C161.179,10.811 161.833,10.044 161.842,9.09L161.842,9.09C161.843,9.02 161.84,8.949 161.834,8.88L161.834,8.88C161.755,8.012 161.146,7.35 160.417,7.34L160.417,7.34C160.417,7.34 160.416,7.34 160.416,7.34L160.416,7.34Z M 0,0" />
        <path
            android:pathData="M159.473,11.817l3.216,0l0,-5.494l-3.216,0z"
            android:strokeWidth="1"
            android:fillColor="#FFFFFE"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M158.342,8.343C158.337,8.36 157.82,10.101 158.544,11.967L158.544,11.967C158.594,12.096 158.597,12.245 158.552,12.366L158.552,12.366C158.499,12.503 158.401,12.6 158.259,12.653L158.259,12.653C158.101,12.712 157.923,12.696 157.759,12.607L157.759,12.607C157.595,12.519 157.475,12.432 157.421,12.363L157.421,12.363L157.33,12.362L157.329,12.476C157.396,12.561 157.524,12.655 157.708,12.755L157.708,12.755C157.82,12.815 157.938,12.846 158.054,12.848L158.054,12.848C158.137,12.85 158.218,12.835 158.296,12.807L158.296,12.807C158.474,12.74 158.599,12.615 158.667,12.435L158.667,12.435C158.73,12.27 158.727,12.07 158.66,11.897L158.66,11.897C157.961,10.095 158.457,8.417 158.462,8.4L158.462,8.4C158.475,8.359 158.458,8.313 158.425,8.297L158.425,8.297C158.417,8.293 158.41,8.291 158.402,8.291L158.402,8.291C158.376,8.291 158.352,8.311 158.342,8.343L158.342,8.343Z M 0,0" />
        <path
            android:pathData="M156.464,13.907l3.096,0l0,-6.675l-3.096,0z"
            android:strokeWidth="1"
            android:fillColor="#DA7961"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M157.402,8.558C157.339,8.606 157.336,8.687 157.393,8.739L157.393,8.739C157.451,8.791 157.549,8.794 157.611,8.746L157.611,8.746C157.772,8.622 157.926,8.565 158.065,8.579L158.065,8.579C158.263,8.598 158.391,8.75 158.395,8.755L158.395,8.755C158.424,8.792 158.472,8.812 158.522,8.812L158.522,8.812L158.608,8.791C158.651,8.767 158.676,8.729 158.677,8.688L158.677,8.688L158.677,8.68C158.676,8.657 158.668,8.634 158.652,8.613L158.652,8.613C158.644,8.603 158.447,8.359 158.104,8.324L158.104,8.324C158.077,8.321 158.049,8.32 158.022,8.32L158.022,8.32C157.814,8.32 157.606,8.4 157.402,8.558L157.402,8.558Z M 0,0" />
        <path
            android:pathData="M156.336,9.66l3.358,0l0,-2.187l-3.358,0z"
            android:strokeWidth="1"
            android:fillColor="#0A0B09"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M152.956,0.213C150.793,0.224 149.326,1.381 148.435,2.447L148.435,2.447C147.822,3.178 147.431,4.061 147.259,4.993L147.259,4.993C147.075,5.997 146.419,7.714 144.126,8.684L144.126,8.684C141.638,9.737 141.99,12.307 142.349,13.588L142.349,13.588C142.482,14.065 142.414,14.573 142.155,14.997L142.155,14.997C141.569,15.953 141.349,16.825 141.344,17.588L141.344,17.588L141.344,17.645C141.348,18.222 141.474,18.736 141.654,19.18L141.654,19.18C142.04,20.13 143.011,20.561 143.836,21.188L143.836,21.188C144.82,21.933 142.807,25.903 145.653,25.935L145.653,25.935C147.4,25.956 146.425,28.92 148.454,28.921L148.454,28.921L148.455,28.921C148.536,28.921 148.622,28.916 148.712,28.906L148.712,28.906C150.209,28.744 149.393,25.644 149.959,24.261L149.959,24.261C150.4,23.183 151.158,22.269 152.097,21.56L152.097,21.56C154.257,19.93 153.539,14.981 152.78,11.74L152.78,11.74C152.3,9.69 153.018,7.548 154.644,6.175L154.644,6.175C157.502,3.762 159.868,2.201 155.439,0.659L155.439,0.659C154.549,0.35 153.742,0.217 153.013,0.213L153.013,0.213L152.956,0.213Z M 0,0" />
        <path
            android:pathData="M140.384,29.878l18.472,0l0,-30.622l-18.472,0z"
            android:strokeWidth="1"
            android:fillColor="#101523"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M155.204,9.199C155.166,9.23 155.16,9.285 155.19,9.324L155.19,9.324C155.204,9.341 155.536,9.749 156.011,9.768L156.011,9.768C156.068,9.771 156.125,9.767 156.182,9.758L156.182,9.758C156.402,9.722 156.612,9.598 156.809,9.39L156.809,9.39C156.842,9.354 156.84,9.298 156.805,9.264L156.805,9.264C156.769,9.231 156.713,9.233 156.68,9.268L156.68,9.268C156.47,9.492 156.248,9.6 156.019,9.591L156.019,9.591C155.625,9.576 155.331,9.216 155.328,9.213L155.328,9.213C155.311,9.191 155.285,9.18 155.259,9.18L155.259,9.18C155.24,9.18 155.22,9.186 155.204,9.199L155.204,9.199Z M 0,0" />
        <path
            android:pathData="M154.324,10.616l3.356,0l0,-2.283l-3.356,0z"
            android:strokeWidth="1"
            android:fillColor="#DA7961"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M158.226,9.133C158.18,9.179 158.173,9.262 158.21,9.319L158.21,9.319C158.223,9.34 158.539,9.827 158.994,9.85L158.994,9.85C159.05,9.853 159.105,9.849 159.159,9.837L159.159,9.837C159.37,9.794 159.57,9.648 159.758,9.398L159.758,9.398C159.798,9.345 159.796,9.261 159.753,9.212L159.753,9.212C159.711,9.161 159.644,9.163 159.604,9.217L159.604,9.217C159.412,9.472 159.21,9.595 159.004,9.585L159.004,9.585C158.648,9.568 158.38,9.162 158.375,9.154L158.375,9.154C158.354,9.121 158.323,9.104 158.292,9.104L158.292,9.104C158.269,9.104 158.246,9.113 158.226,9.133L158.226,9.133Z M 0,0" />
        <path
            android:pathData="M157.17,11.122l3.634,0l0,-3.288l-3.634,0z"
            android:strokeWidth="1"
            android:fillColor="#DA7961"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M148.58,4.193C147.054,4.93 146.72,6.069 145.64,6.857L145.64,6.857C143.969,8.073 140.456,7.257 139.348,9.143L139.348,9.143C138.483,10.618 139.71,12.608 137.997,13.864L137.997,13.864C136.528,14.942 134.523,14.682 133.274,16.132L133.274,16.132C130.604,19.233 134.326,26.873 138.196,27.648L138.196,27.648C140.929,28.196 141.326,26.251 142.757,24.775L142.757,24.775C143.506,24.003 144.097,24.09 144.752,24.176L144.752,24.176C145.191,24.234 145.659,24.291 146.222,24.092L146.222,24.092C147.798,23.533 148.158,21.508 149.599,20.703L149.599,20.703C151.549,19.615 152.519,18.756 153.165,16.461L153.165,16.461C153.734,14.438 151.755,12.865 151.393,11L151.393,11C151.145,9.724 151.308,8.369 152.07,7.301L152.07,7.301C152.649,6.49 155.536,5.693 154.493,4.288L154.493,4.288C154.131,3.8 153.63,3.484 153.075,3.314L153.075,3.314C152.715,3.204 152.354,3.156 151.995,3.156L151.995,3.156C150.804,3.156 149.634,3.683 148.58,4.193L148.58,4.193Z M 0,0" />
        <path
            android:pathData="M131.395,28.692l24.297,0l0,-26.484l-24.297,0z"
            android:strokeWidth="1"
            android:fillColor="#101523"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <path
        android:pathData="M178.468,92.813C178.153,92.593 177.777,92.703 177.438,92.567C176.609,92.232 176.142,91.432 175.61,90.704C175.427,90.454 175.235,90.212 175.019,90C174.873,91.745 175.577,93.231 177.073,93.749C177.411,93.864 178.771,94.254 178.981,93.744C179.097,93.462 178.65,92.94 178.468,92.813"
        android:strokeWidth="1"
        android:fillColor="#121322"
        android:fillType="evenOdd"
        android:strokeColor="#00000000" />
    <group>
        <clip-path android:pathData="M177.566,93.571C177.94,93.675 179.445,94.025 179.677,93.565L179.677,93.565C179.805,93.312 179.311,92.842 179.11,92.727L179.11,92.727C178.761,92.529 178.346,92.629 177.97,92.506L177.97,92.506C177.053,92.204 176.536,91.484 175.947,90.828L175.947,90.828C175.745,90.602 175.532,90.384 175.293,90.193L175.293,90.193C175.132,91.765 175.911,93.104 177.566,93.571L177.566,93.571Z M 0,0" />
        <path
            android:pathData="M7.645,194.62l230.485,0l0,-191.483l-230.485,0z"
            android:strokeWidth="1"
            android:fillColor="#121322"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M177.566,93.571C177.94,93.675 179.445,94.025 179.677,93.565L179.677,93.565C179.805,93.312 179.311,92.842 179.11,92.727L179.11,92.727C178.761,92.529 178.346,92.629 177.97,92.506L177.97,92.506C177.053,92.204 176.536,91.484 175.947,90.828L175.947,90.828C175.745,90.602 175.532,90.384 175.293,90.193L175.293,90.193C175.132,91.765 175.911,93.104 177.566,93.571L177.566,93.571Z M 0,0" />
        <path
            android:pathData="M174.295,94.721l6.381,0l0,-5.452l-6.381,0z"
            android:strokeWidth="1"
            android:fillColor="#121322"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M174.194,83.964C173.66,87.083 172.728,86.273 173.502,87.434L173.502,87.434C173.981,88.151 174.449,89.547 175.044,90.152L175.044,90.152C175.326,90.439 175.437,90.708 175.648,91.025L175.648,91.025C175.918,91.434 176.394,91.65 176.782,91.928L176.782,91.928C177.121,92.169 177.623,92.674 178.078,92.696L178.078,92.696C178.705,92.727 178.358,92.077 178.216,91.776L178.216,91.776C177.944,91.205 177.631,90.689 177.537,90.045L177.537,90.045C177.232,87.972 177.922,85.353 178.907,83.474L178.907,83.474L173.322,81.091C173.19,81.887 174.283,83.44 174.194,83.964L174.194,83.964Z M 0,0" />
        <path
            android:pathData="M172.19,93.681l7.734,0l0,-13.574l-7.734,0z"
            android:strokeWidth="1"
            android:fillColor="#E8B194"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M172.508,89.028C172.927,89.596 173.483,89.761 173.992,90.149L173.992,90.149C174.493,90.533 174.685,91.196 175.016,91.726L175.016,91.726C175.385,92.31 175.902,92.734 176.492,93.031L176.492,93.031C176.663,93.117 178.654,93.864 178.642,93.762L178.642,93.762C178.582,93.184 178.246,92.581 177.952,92.04L177.952,92.04C177.768,91.7 177.503,91.066 177.181,90.868L177.181,90.868C177.196,91.185 177.467,91.616 177.329,91.908L177.329,91.908C176.724,91.673 176.213,91.124 175.777,90.63L175.777,90.63C175.391,90.192 175.081,89.692 174.762,89.194L174.762,89.194C174.606,88.948 172.812,86.216 173.057,86.052L173.057,86.052C172.195,86.581 171.892,88.187 172.508,89.028 M 0,0" />
        <path
            android:pathData="M172.508,89.028C172.927,89.596 173.483,89.761 173.992,90.149L173.992,90.149C174.493,90.533 174.685,91.196 175.016,91.726L175.016,91.726C175.385,92.31 175.902,92.734 176.492,93.031L176.492,93.031C176.663,93.117 178.654,93.864 178.642,93.762L178.642,93.762C178.582,93.184 178.246,92.581 177.952,92.04L177.952,92.04C177.768,91.7 177.503,91.066 177.181,90.868L177.181,90.868C177.196,91.185 177.467,91.616 177.329,91.908L177.329,91.908C176.724,91.673 176.213,91.124 175.777,90.63L175.777,90.63C175.391,90.192 175.081,89.692 174.762,89.194L174.762,89.194C174.606,88.948 172.812,86.216 173.057,86.052L173.057,86.052C172.195,86.581 171.892,88.187 172.508,89.028"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="89.95803"
                    android:startX="171.89197"
                    android:endY="89.95803"
                    android:endX="178.65413"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFD958E" />
                    <item
                        android:offset="1"
                        android:color="#FFF67C77" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M172.508,89.028C172.927,89.596 173.483,89.761 173.992,90.149L173.992,90.149C174.493,90.533 174.685,91.196 175.016,91.726L175.016,91.726C175.385,92.31 175.902,92.734 176.492,93.031L176.492,93.031C176.663,93.117 178.654,93.864 178.642,93.762L178.642,93.762C178.582,93.184 178.246,92.581 177.952,92.04L177.952,92.04C177.768,91.7 177.503,91.066 177.181,90.868L177.181,90.868C177.196,91.185 177.467,91.616 177.329,91.908L177.329,91.908C176.724,91.673 176.213,91.124 175.777,90.63L175.777,90.63C175.391,90.192 175.081,89.692 174.762,89.194L174.762,89.194C174.606,88.948 172.812,86.216 173.057,86.052L173.057,86.052C172.195,86.581 171.892,88.187 172.508,89.028 M 0,0" />
        <path
            android:pathData="M172.508,89.028C172.927,89.596 173.483,89.761 173.992,90.149L173.992,90.149C174.493,90.533 174.685,91.196 175.016,91.726L175.016,91.726C175.385,92.31 175.902,92.734 176.492,93.031L176.492,93.031C176.663,93.117 178.654,93.864 178.642,93.762L178.642,93.762C178.582,93.184 178.246,92.581 177.952,92.04L177.952,92.04C177.768,91.7 177.503,91.066 177.181,90.868L177.181,90.868C177.196,91.185 177.467,91.616 177.329,91.908L177.329,91.908C176.724,91.673 176.213,91.124 175.777,90.63L175.777,90.63C175.391,90.192 175.081,89.692 174.762,89.194L174.762,89.194C174.606,88.948 172.812,86.216 173.057,86.052L173.057,86.052C172.195,86.581 171.892,88.187 172.508,89.028"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="99.93097"
                    android:startX="6.1635427"
                    android:endY="99.93097"
                    android:endX="237.33128"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFD958E" />
                    <item
                        android:offset="1"
                        android:color="#FFF67C77" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M172.508,89.028C172.927,89.596 173.483,89.761 173.992,90.149L173.992,90.149C174.493,90.533 174.685,91.196 175.016,91.726L175.016,91.726C175.385,92.31 175.902,92.734 176.492,93.031L176.492,93.031C176.663,93.117 178.654,93.864 178.642,93.762L178.642,93.762C178.582,93.184 178.246,92.581 177.952,92.04L177.952,92.04C177.768,91.7 177.503,91.066 177.181,90.868L177.181,90.868C177.196,91.185 177.467,91.616 177.329,91.908L177.329,91.908C176.724,91.673 176.213,91.124 175.777,90.63L175.777,90.63C175.391,90.192 175.081,89.692 174.762,89.194L174.762,89.194C174.606,88.948 172.812,86.216 173.057,86.052L173.057,86.052C172.195,86.581 171.892,88.187 172.508,89.028 M 0,0" />
        <path
            android:pathData="M172.508,89.028C172.927,89.596 173.483,89.761 173.992,90.149L173.992,90.149C174.493,90.533 174.685,91.196 175.016,91.726L175.016,91.726C175.385,92.31 175.902,92.734 176.492,93.031L176.492,93.031C176.663,93.117 178.654,93.864 178.642,93.762L178.642,93.762C178.582,93.184 178.246,92.581 177.952,92.04L177.952,92.04C177.768,91.7 177.503,91.066 177.181,90.868L177.181,90.868C177.196,91.185 177.467,91.616 177.329,91.908L177.329,91.908C176.724,91.673 176.213,91.124 175.777,90.63L175.777,90.63C175.391,90.192 175.081,89.692 174.762,89.194L174.762,89.194C174.606,88.948 172.812,86.216 173.057,86.052L173.057,86.052C172.195,86.581 171.892,88.187 172.508,89.028"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="89.95797"
                    android:startX="170.91167"
                    android:endY="89.95797"
                    android:endX="179.63446"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFD958E" />
                    <item
                        android:offset="1"
                        android:color="#FFF67C77" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M176.289,84.385C178.365,85.287 179.66,83.899 179.653,81.978L179.653,81.978C178.489,82.925 174.557,81.793 173.672,80.226L173.672,80.226C172.6,82.894 174.361,83.547 176.289,84.385L176.289,84.385Z M 0,0" />
        <path
            android:pathData="M172.41,85.644l8.179,0l0,-6.395l-8.179,0z"
            android:strokeWidth="1"
            android:fillColor="#121322"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M155.306,12.468C155.238,12.539 155.228,12.667 155.286,12.752L155.286,12.752C155.669,13.325 156.574,14.079 157.703,13.734L157.703,13.734C158.015,13.638 158.344,13.458 158.684,13.166L158.684,13.166C158.758,13.104 158.777,12.978 158.726,12.886L158.726,12.886C158.676,12.794 158.575,12.77 158.502,12.832L158.502,12.832C156.788,14.302 155.585,12.568 155.534,12.493L155.534,12.493C155.502,12.445 155.456,12.421 155.41,12.421L155.41,12.421C155.373,12.421 155.336,12.436 155.306,12.468L155.306,12.468Z M 0,0" />
        <path
            android:pathData="M154.232,15.092l5.54,0l0,-3.942l-5.54,0z"
            android:strokeWidth="1"
            android:fillColor="#DA7961"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M159.911,32.661C159.918,32.817 160.096,32.905 160.228,32.819L160.228,32.819L163.262,30.835C163.658,30.417 163.781,29.81 163.578,29.274L163.578,29.274C161.415,23.549 159.374,22.214 159.374,22.214L159.374,22.214L159.911,32.661Z M 0,0" />
        <path
            android:pathData="M159.911,32.661C159.918,32.817 160.096,32.905 160.228,32.819L160.228,32.819L163.262,30.835C163.658,30.417 163.781,29.81 163.578,29.274L163.578,29.274C161.415,23.549 159.374,22.214 159.374,22.214L159.374,22.214L159.911,32.661Z"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="27.559475"
                    android:startX="159.37404"
                    android:endY="27.559475"
                    android:endX="163.78102"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFC1C1" />
                    <item
                        android:offset="1"
                        android:color="#FFFD958E" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M159.911,32.661C159.918,32.817 160.096,32.905 160.228,32.819L160.228,32.819L163.262,30.835C163.658,30.417 163.781,29.81 163.578,29.274L163.578,29.274C161.415,23.549 159.374,22.214 159.374,22.214L159.374,22.214L159.911,32.661Z M 0,0" />
        <path
            android:pathData="M159.911,32.661C159.918,32.817 160.096,32.905 160.228,32.819L160.228,32.819L163.262,30.835C163.658,30.417 163.781,29.81 163.578,29.274L163.578,29.274C161.415,23.549 159.374,22.214 159.374,22.214L159.374,22.214L159.911,32.661Z"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="100.30037"
                    android:startX="3.652028"
                    android:endY="100.30037"
                    android:endX="239.84264"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFC1C1" />
                    <item
                        android:offset="1"
                        android:color="#FFFD958E" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M159.911,32.661C159.918,32.817 160.096,32.905 160.228,32.819L160.228,32.819L163.262,30.835C163.658,30.417 163.781,29.81 163.578,29.274L163.578,29.274C161.415,23.549 159.374,22.214 159.374,22.214L159.374,22.214L159.911,32.661Z M 0,0" />
        <path
            android:pathData="M159.911,32.661C159.918,32.817 160.096,32.905 160.228,32.819L160.228,32.819L163.262,30.835C163.658,30.417 163.781,29.81 163.578,29.274L163.578,29.274C161.415,23.549 159.374,22.214 159.374,22.214L159.374,22.214L159.911,32.661Z"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="27.559464"
                    android:startX="158.37253"
                    android:endY="27.559464"
                    android:endX="164.78262"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FFFFC1C1" />
                    <item
                        android:offset="1"
                        android:color="#FFFD958E" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M52.353,177.08l0.013,0.032l-0.022,-0.1z M 0,0" />
        <path
            android:pathData="M51.333,178.123l2.044,0l0,-2.122l-2.044,0z"
            android:strokeWidth="1"
            android:fillColor="#EEBBA3"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M27.567,200.429l0.074,0l-0.11,-0.008z M 0,0" />
        <path
            android:pathData="M26.52,201.441l2.132,0l0,-2.031l-2.132,0z"
            android:strokeWidth="1"
            android:fillColor="#EEBBA3"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <path
        android:pathData="M53,124L45.141,124.956L37.744,127.733L35,144.545C35,144.545 37.706,148.105 40.722,144.089C43.737,140.072 44.999,135.382 53,124"
        android:strokeWidth="1"
        android:fillColor="#1D72DB"
        android:fillType="evenOdd"
        android:strokeColor="#00000000" />
    <group>
        <clip-path android:pathData="M45.11,125.95L37.887,128.685L35.207,145.243C35.207,145.243 37.849,148.749 40.794,144.793L40.794,144.793C43.739,140.838 44.971,136.218 52.784,125.009L52.784,125.009L45.11,125.95Z M 0,0" />
        <path
            android:pathData="M2.03,213.932l175.18,0l0,-158.945l-175.18,0z"
            android:strokeWidth="1"
            android:fillColor="#1D72DB"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M45.11,125.95L37.887,128.685L35.207,145.243C35.207,145.243 37.849,148.749 40.794,144.793L40.794,144.793C43.739,140.838 44.971,136.218 52.784,125.009L52.784,125.009L45.11,125.95Z M 0,0" />
        <path
            android:pathData="M34.464,147.443l19.063,0l0,-23.201l-19.063,0z"
            android:strokeWidth="1"
            android:fillColor="#1D72DB"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M55.47,111.326C55.375,111.403 55.361,111.544 55.438,111.639L55.438,111.639C55.468,111.675 55.732,111.984 56.105,111.984L56.105,111.984L56.115,111.984C56.275,111.983 56.51,111.92 56.73,111.634L56.73,111.634C56.805,111.537 56.788,111.398 56.689,111.322L56.689,111.322C56.593,111.248 56.452,111.265 56.378,111.363L56.378,111.363C56.288,111.478 56.198,111.538 56.11,111.539L56.11,111.539L56.105,111.539C55.966,111.539 55.825,111.408 55.782,111.356L55.782,111.356C55.738,111.303 55.674,111.276 55.61,111.276L55.61,111.276C55.561,111.276 55.511,111.292 55.47,111.326L55.47,111.326Z M 0,0" />
        <path
            android:pathData="M54.376,112.996l3.411,0l0,-2.732l-3.411,0z"
            android:strokeWidth="1"
            android:fillColor="#DA7961"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M52.392,112.064C52.312,112.087 52.259,112.205 52.276,112.325L52.276,112.325C52.291,112.44 52.363,112.516 52.438,112.502L52.438,112.502C52.449,112.504 52.519,112.522 52.614,112.712L52.614,112.712C52.643,112.771 52.688,112.802 52.733,112.802L52.733,112.802C52.764,112.802 52.795,112.787 52.822,112.757L52.822,112.757C52.888,112.683 52.901,112.544 52.852,112.445L52.852,112.445C52.683,112.107 52.519,112.057 52.438,112.057L52.438,112.057C52.416,112.057 52.4,112.061 52.392,112.064L52.392,112.064Z M 0,0" />
        <path
            android:pathData="M51.598,113.813l1.957,0l0,-2.767l-1.957,0z"
            android:strokeWidth="1"
            android:fillColor="#DA7961"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M54.361,116.641C55.072,116.767 58.329,117.382 58.581,118.2L58.581,118.2C58.865,119.119 58.68,118.474 58.68,118.474L58.68,118.474L58.734,118.086L58.74,115.216C58.74,115.216 55.892,116.84 54.361,116.641L54.361,116.641ZM54.361,116.641C54.273,116.626 54.224,116.618 54.224,116.618L54.224,116.618C54.269,116.628 54.314,116.635 54.361,116.641L54.361,116.641Z M 0,0" />
        <path
            android:pathData="M53.382,119.503l6.202,0l0,-5.096l-6.202,0z"
            android:strokeWidth="1"
            android:fillColor="#0A0B09"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M55.76,111.254C54.326,111.254 53.165,112.356 53.165,113.715L53.165,113.715L53.165,119.536C53.165,120.895 54.326,121.997 55.76,121.997L55.76,121.997L56.319,121.997C57.752,121.997 58.914,120.895 58.914,119.536L58.914,119.536L58.914,113.715C58.914,112.356 57.752,111.254 56.319,111.254L56.319,111.254L55.76,111.254Z M 0,0" />
        <path
            android:pathData="M52.356,122.764l7.368,0l0,-12.277l-7.368,0z"
            android:strokeWidth="1"
            android:fillColor="#EFBBA3"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M55.784,116.736C57.111,117.055 58.767,117.644 58.75,118.6L58.75,118.6L58.879,118.708L58.91,118.474L58.918,115.338C58.918,115.338 58.129,116.86 55.784,116.736L55.784,116.736ZM55.784,116.736C54.9,116.524 54.162,116.432 54.162,116.432L54.162,116.432C54.771,116.618 55.309,116.711 55.784,116.736L55.784,116.736Z M 0,0" />
        <path
            android:pathData="M53.319,119.516l6.441,0l0,-4.987l-6.441,0z"
            android:strokeWidth="1"
            android:fillColor="#B08477"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M57.523,111.36L57.102,112.629C57.102,112.629 58.015,112.952 58.647,112.399L58.647,112.399L58.692,112.358C59.164,111.9 58.605,111.326 57.843,111.326L57.843,111.326C57.739,111.326 57.632,111.337 57.523,111.36L57.523,111.36Z M 0,0" />
        <path
            android:pathData="M56.091,113.397l3.786,0l0,-2.745l-3.786,0z"
            android:strokeWidth="1"
            android:fillColor="#B08477"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M55.722,108.09C54.316,108.09 53.177,109.184 53.177,110.533L53.177,110.533L53.177,114.251C53.177,115.601 54.316,116.695 55.722,116.695L55.722,116.695L56.271,116.695C57.677,116.695 58.816,115.601 58.816,114.251L58.816,114.251L58.816,110.533C58.816,109.184 57.677,108.09 56.271,108.09L56.271,108.09L55.722,108.09Z M 0,0" />
        <path
            android:pathData="M52.418,117.423l7.156,0l0,-10.061l-7.156,0z"
            android:strokeWidth="1"
            android:fillColor="#EFBBA3"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M57.568,111.078L57.568,111.797C57.568,112.292 57.623,112.781 57.732,113.25L57.732,113.25L57.74,113.285C57.762,113.37 57.735,113.503 57.626,113.609L57.626,113.609C57.557,113.675 57.473,113.7 57.387,113.677L57.387,113.677C57.297,113.654 57.22,113.583 57.175,113.482L57.175,113.482C57.141,113.407 57.116,113.318 57.099,113.215L57.099,113.215L57.04,113.166L57.002,113.242C57.021,113.358 57.05,113.46 57.089,113.545L57.089,113.545C57.149,113.678 57.25,113.771 57.367,113.802L57.367,113.802L57.448,113.812C57.533,113.812 57.617,113.777 57.685,113.711L57.685,113.711C57.844,113.556 57.865,113.363 57.834,113.244L57.834,113.244L57.827,113.214C57.721,112.756 57.667,112.279 57.667,111.797L57.667,111.797L57.667,111.078L57.617,111.014L57.568,111.078Z M 0,0" />
        <path
            android:pathData="M56.327,114.678l2.194,0l0,-4.531l-2.194,0z"
            android:strokeWidth="1"
            android:fillColor="#DA7961"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M52.513,112.522L52.549,112.572C53.054,113.236 53.785,112.848 53.785,112.848L53.785,112.848L53.448,111.325C53.362,111.298 53.276,111.285 53.193,111.284L53.193,111.284C52.583,111.284 52.135,111.973 52.513,112.522L52.513,112.522Z M 0,0" />
        <path
            android:pathData="M51.565,113.77l3.029,0l0,-3.294l-3.029,0z"
            android:strokeWidth="1"
            android:fillColor="#EFBBA3"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M53.181,111.607C53.307,111.6 53.355,112.109 53.331,112.421L53.331,112.421C53.32,112.568 53.435,112.694 53.582,112.694L53.582,112.694L53.618,112.694C53.764,112.694 53.888,112.584 53.906,112.438L53.906,112.438C53.925,112.277 53.932,112.139 53.931,112.02L53.931,112.02C53.927,111.715 53.802,111.428 53.592,111.208L53.592,111.208C53.167,110.762 53.186,110.252 53.186,110.252L53.186,110.252C53.186,110.252 52.922,111.62 53.181,111.607L53.181,111.607Z M 0,0" />
        <path
            android:pathData="M52.393,113.561l2.212,0l0,-4.176l-2.212,0z"
            android:strokeWidth="1"
            android:fillColor="#464442"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M55.041,109.591L55.037,109.696L55.127,109.7C55.144,109.681 55.566,109.242 56.027,109.701L56.027,109.701C56.209,109.884 56.389,109.946 56.547,109.946L56.547,109.946C56.862,109.946 57.093,109.702 57.099,109.696L57.099,109.696C57.112,109.678 57.427,109.29 57.811,109.7L57.811,109.7L57.901,109.696L57.898,109.591C57.748,109.431 57.589,109.354 57.427,109.364L57.427,109.364C57.178,109.377 57.012,109.586 57.01,109.591L57.01,109.591C56.991,109.61 56.57,110.049 56.109,109.589L56.109,109.589C55.927,109.407 55.747,109.345 55.589,109.345L55.589,109.345C55.274,109.345 55.044,109.587 55.041,109.591L55.041,109.591Z M 0,0" />
        <path
            android:pathData="M54.153,110.956l4.631,0l0,-2.622l-4.631,0z"
            android:strokeWidth="1"
            android:fillColor="#DA7961"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M56.124,109.134L56.135,109.237C56.138,109.241 56.327,109.439 56.553,109.439L56.553,109.439C56.668,109.439 56.792,109.388 56.906,109.233L56.906,109.233L56.909,109.128L56.84,109.123C56.562,109.502 56.208,109.133 56.193,109.117L56.193,109.117L56.124,109.134Z M 0,0" />
        <path
            android:pathData="M55.44,110.451l2.156,0l0,-2.359l-2.156,0z"
            android:strokeWidth="1"
            android:fillColor="#DA7961"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M55.143,110.164C55.049,110.281 55.068,110.452 55.184,110.545L55.184,110.545C55.206,110.562 55.584,110.859 56.11,110.859L56.11,110.859C56.291,110.859 56.489,110.824 56.697,110.73L56.697,110.73C56.834,110.669 56.894,110.509 56.832,110.372L56.832,110.372C56.771,110.236 56.61,110.174 56.474,110.237L56.474,110.237C55.973,110.463 55.544,110.138 55.523,110.122L55.523,110.122C55.473,110.082 55.413,110.063 55.354,110.063L55.354,110.063C55.275,110.063 55.196,110.098 55.143,110.164L55.143,110.164Z M 0,0" />
        <path
            android:pathData="M54.409,111.533l3.122,0l0,-2.144l-3.122,0z"
            android:strokeWidth="1"
            android:fillColor="#3D3D3B"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M57.26,110.129C57.119,110.18 57.047,110.336 57.098,110.476L57.098,110.476C57.149,110.617 57.305,110.688 57.446,110.638L57.446,110.638C57.451,110.636 57.934,110.465 58.335,110.884L58.335,110.884C58.388,110.939 58.46,110.967 58.531,110.967L58.531,110.967C58.598,110.967 58.666,110.942 58.718,110.892L58.718,110.892C58.826,110.789 58.83,110.617 58.726,110.509L58.726,110.509C58.379,110.147 57.989,110.061 57.698,110.061L57.698,110.061C57.45,110.061 57.275,110.123 57.26,110.129L57.26,110.129Z M 0,0" />
        <path
            android:pathData="M56.408,111.641l3.068,0l0,-2.254l-3.068,0z"
            android:strokeWidth="1"
            android:fillColor="#3D3D3B"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M52.117,112.166L52.06,112.252L52.144,112.312C52.146,112.312 52.322,112.29 52.54,112.581L52.54,112.581L52.599,112.611L52.643,112.596L52.659,112.493C52.438,112.199 52.241,112.163 52.157,112.163L52.157,112.163C52.136,112.163 52.123,112.165 52.117,112.166L52.117,112.166Z M 0,0" />
        <path
            android:pathData="M51.047,113.622l2.638,0l0,-2.47l-2.638,0z"
            android:strokeWidth="1"
            android:fillColor="#DA7961"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M56.979,113.336C56.593,113.604 56.266,113.412 56.2,113.368L56.2,113.368C55.92,113.167 55.53,113.227 55.324,113.505L55.324,113.505C55.116,113.785 55.175,114.181 55.455,114.389L55.455,114.389C55.685,114.56 56.091,114.743 56.57,114.743L56.57,114.743C56.92,114.743 57.309,114.645 57.698,114.376L57.698,114.376C57.986,114.177 58.057,113.783 57.858,113.496L57.858,113.496C57.735,113.319 57.538,113.224 57.338,113.224L57.338,113.224C57.214,113.224 57.088,113.261 56.979,113.336L56.979,113.336Z M 0,0" />
        <path
            android:pathData="M54.525,115.417l4.119,0l0,-2.867l-4.119,0z"
            android:strokeWidth="1"
            android:fillColor="#3D3D3B"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <path
        android:pathData="M59,118.244L58.195,117L55.501,118.963C55.205,119.179 54.862,119.31 54.506,119.341L54.431,119.348C54.182,119.37 53.936,119.285 53.744,119.111L52.651,117.669L51.649,119.869L49,121.699L52.252,126L58.195,123.149L59,118.244"
        android:strokeWidth="1"
        android:fillColor="#1D72DB"
        android:fillType="evenOdd"
        android:strokeColor="#00000000" />
    <group>
        <clip-path android:pathData="M56.139,119.143C55.822,119.346 55.456,119.469 55.074,119.499L55.074,119.499L54.995,119.505C54.729,119.526 54.465,119.446 54.26,119.283L54.26,119.283L53.09,117.926L52.019,119.995L49.185,121.716L52.663,125.761L59.021,123.08L59.882,118.468L59.021,117.297L56.139,119.143Z M 0,0" />
        <path
            android:pathData="M-1.188,212.421l187.345,0l0,-157.139l-187.345,0z"
            android:strokeWidth="1"
            android:fillColor="#1D72DB"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M56.139,119.143C55.822,119.346 55.456,119.469 55.074,119.499L55.074,119.499L54.995,119.505C54.729,119.526 54.465,119.446 54.26,119.283L54.26,119.283L53.09,117.926L52.019,119.995L49.185,121.716L52.663,125.761L59.021,123.08L59.882,118.468L59.021,117.297L56.139,119.143Z M 0,0" />
        <path
            android:pathData="M48.39,126.519l12.286,0l0,-9.981l-12.286,0z"
            android:strokeWidth="1"
            android:fillColor="#1D72DB"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M54.259,112.579C54.259,112.809 54.539,112.996 54.885,112.996L54.885,112.996C55.23,112.996 55.51,112.809 55.51,112.579L55.51,112.579C55.51,112.349 55.23,112.162 54.885,112.162L54.885,112.162C54.539,112.162 54.259,112.349 54.259,112.579L54.259,112.579Z M 0,0" />
        <path
            android:pathData="M53.249,113.67l3.273,0l0,-2.182l-3.273,0z"
            android:strokeWidth="1"
            android:fillColor="#E29E85"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M57.47,112.911C57.47,113.257 57.75,113.536 58.095,113.536L58.095,113.536C58.441,113.536 58.722,113.257 58.722,112.911L58.722,112.911C58.722,112.565 58.441,112.285 58.095,112.285L58.095,112.285C57.75,112.285 57.47,112.565 57.47,112.911L57.47,112.911Z M 0,0" />
        <path
            android:pathData="M56.458,114.548l3.273,0l0,-3.273l-3.273,0z"
            android:strokeWidth="1"
            android:fillColor="#E29E85"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M57.107,111.313L57.144,111.402C57.148,111.403 57.528,111.531 57.911,111.681L57.911,111.681L57.923,111.683L57.971,111.627L57.936,111.538C57.551,111.387 57.17,111.259 57.166,111.257L57.166,111.257L57.107,111.313Z M 0,0" />
        <path
            android:pathData="M56.431,112.695l2.216,0l0,-2.451l-2.216,0z"
            android:strokeWidth="1"
            android:fillColor="#DA7961"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M44.792,207.004L40.451,209.797C40.367,209.849 40.301,209.923 40.261,210.012L40.261,210.012C40.114,210.336 40.361,210.701 40.722,210.693L40.722,210.693L49.79,210.486L49.695,208.726L51.016,182.136L52.336,153.64L57.428,207.375L52.85,209.601C52.401,209.935 52.641,210.638 53.204,210.638L53.204,210.638L61.969,210.638L62.049,209.506C62.049,209.506 64.501,195.146 63.369,188.475L63.369,188.475L62.237,175.875L63.369,148.174C63.369,148.174 47.809,150.026 43.094,147.247L43.094,147.247L44.792,207.004Z M 0,0" />
        <path
            android:pathData="M39.447,211.449l24.99,0l0,-64.959l-24.99,0z"
            android:strokeWidth="1"
            android:fillColor="#2D2E2C"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M52.612,109.561L50.697,112.096L50.697,112.096L49.739,113.439L49.677,112.317C49.659,111.983 48.988,111.734 48.904,112.393L48.904,112.393C48.733,113.725 49.089,114.687 48.632,115.865L48.632,115.865C48.548,116.082 48.36,116.247 48.223,116.438L48.223,116.438L50.397,117.922C50.533,117.731 50.67,117.539 50.806,117.348L50.806,117.348C51.317,116.632 51.694,116.978 52.406,115.981L52.406,115.981C52.84,115.372 53.275,114.762 53.71,114.153L53.71,114.153C53.832,113.981 53.789,113.743 53.613,113.623L53.613,113.623C53.448,113.511 53.222,113.541 53.094,113.689L53.094,113.689L53.093,113.688C53.063,113.73 53.005,113.74 52.962,113.711L52.962,113.711C52.92,113.682 52.909,113.624 52.939,113.583L52.939,113.583L53.023,113.465C53.146,113.293 53.102,113.055 52.927,112.935L52.927,112.935C52.752,112.815 52.508,112.857 52.386,113.029L52.386,113.029L52.301,113.147C52.272,113.189 52.213,113.199 52.17,113.171L52.17,113.171C52.129,113.142 52.118,113.086 52.145,113.045L52.145,113.045L52.223,112.937C52.346,112.765 52.302,112.526 52.126,112.407L52.126,112.407C51.951,112.287 51.708,112.33 51.585,112.501L51.585,112.501L51.51,112.607C51.48,112.649 51.421,112.659 51.379,112.63L51.379,112.63C51.338,112.602 51.327,112.548 51.353,112.506L51.353,112.506L53.25,109.997C53.372,109.825 53.46,109.587 53.285,109.467L53.285,109.467L53.154,109.338C53.113,109.311 53.068,109.298 53.022,109.298L53.022,109.298C52.872,109.298 52.706,109.43 52.612,109.561L52.612,109.561Z M 0,0" />
        <path
            android:pathData="M47.414,118.713l7.174,0l0,-10.206l-7.174,0z"
            android:strokeWidth="1"
            android:fillColor="#B08477"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M52.512,109.334L50.597,111.869L50.596,111.868L49.638,113.212L49.577,112.09C49.558,111.755 48.887,111.506 48.803,112.166L48.803,112.166C48.632,113.498 48.988,114.459 48.532,115.637L48.532,115.637C48.447,115.854 48.259,116.019 48.123,116.21L48.123,116.21L50.296,117.694C50.432,117.503 50.569,117.312 50.705,117.121L50.705,117.121C51.216,116.405 51.594,116.75 52.305,115.753L52.305,115.753C52.739,115.144 53.174,114.535 53.609,113.925L53.609,113.925C53.732,113.754 53.688,113.516 53.512,113.396L53.512,113.396C53.347,113.283 53.122,113.314 52.994,113.461L52.994,113.461L52.992,113.46C52.962,113.502 52.904,113.512 52.861,113.484L52.861,113.484C52.819,113.454 52.808,113.397 52.838,113.356L52.838,113.356L52.922,113.237C53.045,113.065 53.002,112.827 52.826,112.707L52.826,112.707C52.651,112.588 52.408,112.63 52.285,112.802L52.285,112.802L52.2,112.92C52.171,112.962 52.112,112.972 52.07,112.943L52.07,112.943C52.028,112.914 52.018,112.859 52.045,112.818L52.045,112.818L52.122,112.709C52.245,112.537 52.202,112.299 52.026,112.179L52.026,112.179C51.85,112.06 51.607,112.102 51.485,112.273L51.485,112.273L51.409,112.379C51.379,112.421 51.32,112.431 51.278,112.402L51.278,112.402C51.237,112.374 51.226,112.319 51.252,112.278L51.252,112.278L53.149,109.769C53.272,109.597 53.228,109.36 53.053,109.239L53.053,109.239C52.986,109.193 52.908,109.171 52.832,109.171L52.832,109.171C52.708,109.171 52.587,109.228 52.512,109.334L52.512,109.334Z M 0,0" />
        <path
            android:pathData="M47.314,118.486l7.174,0l0,-10.105l-7.174,0z"
            android:strokeWidth="1"
            android:fillColor="#EFBBA3"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <path
        android:pathData="M48.516,115.355C45.661,117.24 39.715,121.954 36.054,130.398C36.005,130.511 35.989,130.636 36.008,130.757L38.128,144.435C38.233,145.111 39.193,145.21 39.439,144.57C41.624,138.9 47.346,124.899 52.431,119.76C53.154,119.029 53.193,117.886 52.521,117.112L51.326,115.734C50.626,114.927 49.414,114.762 48.516,115.355"
        android:strokeWidth="1"
        android:fillColor="#1D72DB"
        android:fillType="evenOdd"
        android:strokeColor="#00000000" />
    <group>
        <clip-path android:pathData="M48.357,114.705C45.621,116.627 39.921,121.432 36.413,130.039L36.413,130.039C36.366,130.154 36.351,130.282 36.369,130.406L36.369,130.406L38.401,144.348C38.501,145.038 39.421,145.137 39.658,144.485L39.658,144.485C41.752,138.706 47.236,124.433 52.108,119.195L52.108,119.195C52.801,118.45 52.838,117.285 52.194,116.495L52.194,116.495L51.049,115.092C50.649,114.601 50.075,114.343 49.494,114.343L49.494,114.343C49.1,114.343 48.704,114.461 48.357,114.705L48.357,114.705Z M 0,0" />
        <path
            android:pathData="M2.092,214.446l176.238,0l0,-160.373l-176.238,0z"
            android:strokeWidth="1"
            android:fillColor="#1D72DB"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M48.357,114.705C45.621,116.627 39.921,121.432 36.413,130.039L36.413,130.039C36.366,130.154 36.351,130.282 36.369,130.406L36.369,130.406L38.401,144.348C38.501,145.038 39.421,145.137 39.658,144.485L39.658,144.485C41.752,138.706 47.236,124.433 52.108,119.195L52.108,119.195C52.801,118.45 52.838,117.285 52.194,116.495L52.194,116.495L51.049,115.092C50.649,114.601 50.075,114.343 49.494,114.343L49.494,114.343C49.1,114.343 48.704,114.461 48.357,114.705L48.357,114.705Z M 0,0" />
        <path
            android:pathData="M35.614,145.698l17.787,0l0,-32.129l-17.787,0z"
            android:strokeWidth="1"
            android:fillColor="#1D72DB"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M54.772,122.01L49.73,121.564C49.73,121.564 45.146,126.998 44.046,146.525L44.046,146.525C44.046,146.525 50.005,152.88 63.757,147.446L63.757,147.446L63.39,122.393L59.815,118.34L54.772,122.01Z M 0,0" />
        <path
            android:pathData="M54.772,122.01L49.73,121.564C49.73,121.564 45.146,126.998 44.046,146.525L44.046,146.525C44.046,146.525 50.005,152.88 63.757,147.446L63.757,147.446L63.39,122.393L59.815,118.34L54.772,122.01Z"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="135.60982"
                    android:startX="44.04601"
                    android:endY="135.60982"
                    android:endX="63.7569"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FF1C72DB" />
                    <item
                        android:offset="1"
                        android:color="#FF0E23A3" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M54.772,122.01L49.73,121.564C49.73,121.564 45.146,126.998 44.046,146.525L44.046,146.525C44.046,146.525 50.005,152.88 63.757,147.446L63.757,147.446L63.39,122.393L59.815,118.34L54.772,122.01Z M 0,0" />
        <path
            android:pathData="M54.772,122.01L49.73,121.564C49.73,121.564 45.146,126.998 44.046,146.525L44.046,146.525C44.046,146.525 50.005,152.88 63.757,147.446L63.757,147.446L63.39,122.393L59.815,118.34L54.772,122.01Z"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="135.18246"
                    android:startX="2.2418401"
                    android:endY="135.18246"
                    android:endX="178.85915"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FF1C72DB" />
                    <item
                        android:offset="1"
                        android:color="#FF0E23A3" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <group>
        <clip-path android:pathData="M54.772,122.01L49.73,121.564C49.73,121.564 45.146,126.998 44.046,146.525L44.046,146.525C44.046,146.525 50.005,152.88 63.757,147.446L63.757,147.446L63.39,122.393L59.815,118.34L54.772,122.01Z M 0,0" />
        <path
            android:pathData="M54.772,122.01L49.73,121.564C49.73,121.564 45.146,126.998 44.046,146.525L44.046,146.525C44.046,146.525 50.005,152.88 63.757,147.446L63.757,147.446L63.39,122.393L59.815,118.34L54.772,122.01Z"
            android:strokeWidth="1"
            android:fillType="evenOdd"
            android:strokeColor="#00000000">
            <aapt:attr name="android:fillColor">
                <gradient
                    android:startY="135.60977"
                    android:startX="43.29697"
                    android:endY="135.60977"
                    android:endX="64.50594"
                    android:type="linear">
                    <item
                        android:offset="0"
                        android:color="#FF1C72DB" />
                    <item
                        android:offset="1"
                        android:color="#FF0E23A3" />
                </gradient>
            </aapt:attr>
        </path>
    </group>
    <path
        android:pathData="M61,151.045L65.291,156C65.291,156 70.387,150.578 70.923,137.584C70.923,137.584 71.549,135.153 69.225,130.946L63.503,123C63.503,123 60.553,125.15 62.43,133.938C62.43,133.938 64.045,136.234 63.596,139.506C63.442,140.635 63.316,141.766 63.256,142.905C63.179,144.36 62.716,146.927 61,151.045"
        android:strokeWidth="1"
        android:fillColor="#1D72DB"
        android:fillType="evenOdd"
        android:strokeColor="#00000000" />
    <group>
        <clip-path android:pathData="M61.663,133.372C61.663,133.372 63.358,135.695 62.887,139.005L62.887,139.005C62.725,140.146 62.593,141.291 62.529,142.442L62.529,142.442C62.448,143.914 61.963,146.511 60.16,150.677L60.16,150.677L64.667,155.689C64.667,155.689 70.019,150.204 70.583,137.06L70.583,137.06C70.583,137.06 71.24,134.601 68.799,130.346L68.799,130.346L62.789,122.308C62.789,122.308 59.691,124.483 61.663,133.372L61.663,133.372Z M 0,0" />
        <path
            android:pathData="M0.163,213.86l180.885,0l0,-160.082l-180.885,0z"
            android:strokeWidth="1"
            android:fillColor="#1D72DB"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M61.663,133.372C61.663,133.372 63.358,135.695 62.887,139.005L62.887,139.005C62.725,140.146 62.593,141.291 62.529,142.442L62.529,142.442C62.448,143.914 61.963,146.511 60.16,150.677L60.16,150.677L64.667,155.689C64.667,155.689 70.019,150.204 70.583,137.06L70.583,137.06C70.583,137.06 71.24,134.601 68.799,130.346L68.799,130.346L62.789,122.308C62.789,122.308 59.691,124.483 61.663,133.372L61.663,133.372Z M 0,0" />
        <path
            android:pathData="M59.393,156.461l12.037,0l0,-34.927l-12.037,0z"
            android:strokeWidth="1"
            android:fillColor="#1D72DB"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M59.281,152.383C59.569,153.819 62.007,156.772 62.007,156.772L62.007,156.772L63.748,153.261L61.514,151.151C61.007,152.472 59.281,152.383 59.281,152.383L59.281,152.383Z M 0,0" />
        <path
            android:pathData="M58.503,157.53l6.022,0l0,-7.138l-6.022,0z"
            android:strokeWidth="1"
            android:fillColor="#EFBBA3"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M56.194,111.513C56.194,111.611 56.275,111.691 56.372,111.691L56.372,111.691C56.47,111.691 56.549,111.611 56.549,111.513L56.549,111.513C56.549,111.415 56.47,111.336 56.372,111.336L56.372,111.336C56.275,111.336 56.194,111.415 56.194,111.513L56.194,111.513Z M 0,0" />
        <path
            android:pathData="M55.183,112.701l2.376,0l0,-2.377l-2.376,0z"
            android:strokeWidth="1"
            android:fillColor="#DA7961"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <path
        android:pathData="M36.284,130C36.284,130 33.721,134.81 33.175,138.129C32.682,141.127 33.085,144.52 36.305,145.664C36.688,145.801 37.11,145.905 37.576,145.973C38.775,146.147 39.956,145.47 40.307,144.342C40.32,144.3 40.333,144.256 40.345,144.211C40.859,142.264 45,131.3 45,131.3"
        android:strokeWidth="1"
        android:fillColor="#1D72DB"
        android:fillType="evenOdd"
        android:strokeColor="#00000000" />
    <group>
        <clip-path android:pathData="M33.251,138.699C32.776,141.855 33.164,145.426 36.265,146.63L36.265,146.63C36.633,146.774 37.04,146.883 37.489,146.955L37.489,146.955C38.643,147.139 39.78,146.426 40.119,145.239L40.119,145.239C40.131,145.194 40.144,145.148 40.155,145.101L40.155,145.101C40.651,143.051 44.638,131.511 44.638,131.511L44.638,131.511L36.245,130.144C36.245,130.144 33.777,135.206 33.251,138.699L33.251,138.699Z M 0,0" />
        <path
            android:pathData="M2.545,215.176l173.41,0l0,-161.901l-173.41,0z"
            android:strokeWidth="1"
            android:fillColor="#1D72DB"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
    <group>
        <clip-path android:pathData="M33.251,138.699C32.776,141.855 33.164,145.426 36.265,146.63L36.265,146.63C36.633,146.774 37.04,146.883 37.489,146.955L37.489,146.955C38.643,147.139 39.78,146.426 40.119,145.239L40.119,145.239C40.131,145.194 40.144,145.148 40.155,145.101L40.155,145.101C40.651,143.051 44.638,131.511 44.638,131.511L44.638,131.511L36.245,130.144C36.245,130.144 33.777,135.206 33.251,138.699L33.251,138.699Z M 0,0" />
        <path
            android:pathData="M32.347,147.765l13.026,0l0,-18.403l-13.026,0z"
            android:strokeWidth="1"
            android:fillColor="#1D72DB"
            android:fillType="evenOdd"
            android:strokeColor="#00000000" />
    </group>
</vector>
