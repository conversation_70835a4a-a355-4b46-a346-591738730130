package se.scmv.morocco.ui

import androidx.compose.runtime.Composable
import androidx.compose.runtime.Stable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.navigation.NavDestination
import androidx.navigation.NavDestination.Companion.hasRoute
import androidx.navigation.NavGraph.Companion.findStartDestination
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import androidx.navigation.navOptions
import se.scmv.morocco.account.presentation.navigation.AccountBookmarksRoute
import se.scmv.morocco.account.presentation.navigation.AccountMasterRoute
import se.scmv.morocco.account.presentation.navigation.AccountStatisticsRoute
import se.scmv.morocco.account.presentation.navigation.MessagingRoute
import se.scmv.morocco.ad.navigation.ListingRoute
import se.scmv.morocco.domain.models.Account

@Composable
fun rememberAppState(
    account: Account,
    homeNavController: NavHostController = rememberNavController(),
): AvitoAppState {
    return remember( homeNavController, account) {
        AvitoAppState(
            homeNavController = homeNavController,
            account = account,
        )
    }
}

@Stable
class AvitoAppState(
    val homeNavController: NavHostController,
    val account: Account,
) {
    // NAVIGATION
    private val previousDestination = mutableStateOf<NavDestination?>(null)

    val currentDestination: NavDestination?
        @Composable get() {
            // Collect the currentBackStackEntryFlow as a state
            val currentEntry = homeNavController.currentBackStackEntryFlow
                .collectAsState(initial = null)

            // Fallback to previousDestination if currentEntry is null
            return currentEntry.value?.destination.also { destination ->
                if (destination != null) {
                    previousDestination.value = destination
                }
            } ?: previousDestination.value
        }

    val currentTopLevelDestination: TopLevelDestination?
        @Composable get() {
            return TopLevelDestination.entries().firstOrNull { topLevelDestination ->
                currentDestination?.hasRoute(route = topLevelDestination.route) == true
            }
        }

    /**
     * Map of top level destinations to be used in the TopBar and BottomBar. The key is the route.
     */
    val topLevelDestinations: List<TopLevelDestination> = TopLevelDestination.entries().filter {
        when (account) {
            is Account.Connected.Shop -> it != TopLevelDestination.BOOKMARKS
            else -> it != TopLevelDestination.STATS
        }
    }

    /**
     * UI logic for navigating to a top level destination in the app. Top level destinations have
     * only one copy of the destination of the back stack, and save and restore state whenever you
     * navigate to and from it.
     *
     * @param topLevelDestination: The destination the app needs to navigate to.
     */
    fun navigateToTopLevelDestination(topLevelDestination: TopLevelDestination) {
        val topLevelNavOptions = navOptions {
            // Pop up to the start destination of the graph to
            // avoid building up a large stack of destinations
            // on the back stack as users select items
            popUpTo(homeNavController.graph.findStartDestination().id) {
                saveState = true
            }
            // Avoid multiple copies of the same destination when
            // reselecting the same item
            launchSingleTop = true
            // Restore state when reselecting a previously selected item
            restoreState = true
        }

        when (topLevelDestination) {
            TopLevelDestination.LISTING -> homeNavController.navigate(
                ListingRoute(""),
                topLevelNavOptions
            )

            TopLevelDestination.BOOKMARKS -> homeNavController.navigate(
                AccountBookmarksRoute(),
                topLevelNavOptions
            )

            TopLevelDestination.STATS -> homeNavController.navigate(
                AccountStatisticsRoute,
                topLevelNavOptions
            )

            TopLevelDestination.MESSAGING -> homeNavController.navigate(
                MessagingRoute,
                topLevelNavOptions
            )

            TopLevelDestination.ACCOUNT -> homeNavController.navigate(
                AccountMasterRoute,
                topLevelNavOptions
            )
        }
    }
}